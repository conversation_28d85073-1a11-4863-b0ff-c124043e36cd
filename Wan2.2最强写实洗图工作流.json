{"last_link_id": 296, "nodes": [{"outputs": [{"name": "reel", "links": [278], "label": "reel", "type": "<PERSON><PERSON>", "localized_name": "reel"}], "color": "#323", "widgets_values": ["原图", "洗图", "", "image4", 1536, 8], "inputs": [{"name": "image1", "link": 267, "label": "image1", "type": "IMAGE", "localized_name": "image1"}, {"shape": 7, "name": "image2", "link": 268, "label": "image2", "type": "IMAGE", "localized_name": "image2"}, {"shape": 7, "name": "image3", "label": "image3", "type": "IMAGE", "localized_name": "image3"}, {"shape": 7, "name": "image4", "label": "image4", "type": "IMAGE", "localized_name": "image4"}], "flags": {}, "type": "LayerUtility: ImageReel", "mode": 0, "bgcolor": "#535", "size": [270, 238], "pos": [7815.32275390625, 181.62962341308594], "id": 135, "properties": {"cnr_id": "comfyui_layerstyle", "ver": "3bfe8e435d167b4a5bf08716729f89802dbbaa6f", "widget_ue_connectable": {}, "Node name for S&R": "LayerUtility: ImageReel"}, "order": 20}, {"outputs": [{"name": "CONDITIONING", "links": [282], "label": "CONDITIONING", "type": "CONDITIONING", "localized_name": "CONDITIONING"}], "color": "#232", "widgets_values": ["色调艳丽，过曝，静态，细节模糊不清，字幕，风格，作品，画作，画面，静止，整体发灰，最差质量，低质量，JPEG压缩残留，丑陋的，残缺的，多余的手指，画得不好的手部，画得不好的脸部，畸形的，毁容的，形态畸形的肢体，手指融合，静止不动的画面，杂乱的背景，三条腿，背景人很多，倒着走"], "inputs": [{"name": "clip", "link": 269, "label": "clip", "type": "CLIP", "localized_name": "clip"}], "flags": {"collapsed": false}, "type": "CLIPTextEncode", "mode": 0, "bgcolor": "#353", "size": [450, 150], "pos": [6785.173828125, 407.7565612792969], "id": 136, "properties": {"cnr_id": "comfy-core", "ver": "0.3.43", "widget_ue_connectable": {}, "Node name for S&R": "CLIPTextEncode"}, "order": 14}, {"outputs": [{"name": "CONDITIONING", "links": [281], "label": "CONDITIONING", "type": "CONDITIONING", "localized_name": "CONDITIONING"}], "color": "#232", "widgets_values": ["2010年代初用手机拍摄并上传到Facebook的快照照片, 具有动态自然光照, 以及中性白色色彩平衡和褪色效果, 描绘了一位年轻女性脸部特写, 被透过薄纱窗帘的金色晨光照亮, 从侧面拍摄.\n在紧凑的1:1特写镜头中, 仅可见女性脸庞的一半, 略微转向附近的一扇窗户, 温暖, 清晨的阳光透过白色布帘倾泻而入.光线柔和地掠过她的面容, 突显出她脸颊上细腻的桃色绒毛和嘴唇附近干燥皮肤的微弱纹理.她的眼睛在画框边缘刚刚可见, 捕捉到一丝琥珀色光芒.漂浮在空气中的杂乱发丝在逆光中发光.背景模糊不清但显然是室内--可能是卧室--失焦并染上日黄色调.她表情中的轻微眯眼可见清晨的昏沉感.镜头在她脸部最亮的部分周围形成淡淡的光晕, 光线最强的区域有轻微的色彩溢出.感觉非常亲密, 像是醒来后拍下的第一张照片, 在寂静中捕捉."], "inputs": [{"name": "clip", "link": 270, "label": "clip", "type": "CLIP", "localized_name": "clip"}, {"widget": {"name": "text"}, "name": "text", "link": 271, "label": "text", "type": "STRING"}], "flags": {}, "type": "CLIPTextEncode", "mode": 0, "bgcolor": "#353", "size": [437.6938171386719, 146.9487762451172], "pos": [6781.046875, 187.84251403808594], "id": 137, "properties": {"cnr_id": "comfy-core", "ver": "0.3.43", "widget_ue_connectable": {}, "Node name for S&R": "CLIPTextEncode"}, "order": 15}, {"outputs": [{"name": "image", "links": [287], "label": "image", "type": "IMAGE", "localized_name": "image"}, {"name": "mask", "label": "mask", "type": "MASK", "localized_name": "mask"}, {"name": "original_size", "label": "original_size", "type": "BOX", "localized_name": "original_size"}, {"name": "width", "label": "width", "type": "INT", "localized_name": "width"}, {"name": "height", "label": "height", "type": "INT", "localized_name": "height"}], "color": "#223", "widgets_values": ["original", 1, 1, "letterbox", "lanc<PERSON>s", "8", "longest", 1536, "#000000"], "inputs": [{"shape": 7, "name": "image", "link": 273, "label": "image", "type": "IMAGE", "localized_name": "image"}, {"shape": 7, "name": "mask", "label": "mask", "type": "MASK", "localized_name": "mask"}], "flags": {}, "type": "LayerUtility: ImageScaleByAspectRatio V2", "mode": 0, "bgcolor": "#335", "size": [336, 330], "pos": [4672.52978515625, 178.34056091308594], "id": 140, "properties": {"cnr_id": "comfyui_layerstyle", "ver": "3bfe8e435d167b4a5bf08716729f89802dbbaa6f", "widget_ue_connectable": {}, "Node name for S&R": "LayerUtility: ImageScaleByAspectRatio V2"}, "order": 6}, {"outputs": [{"name": "MODEL", "links": [276], "label": "MODEL", "type": "MODEL", "localized_name": "MODEL"}, {"name": "CLIP", "links": [277], "label": "CLIP", "type": "CLIP", "localized_name": "CLIP"}], "color": "#332922", "widgets_values": ["Wan2.1_T2V_14B_FusionX_LoRA.safetensors", 0.4000000000000001, 0.4000000000000001], "inputs": [{"name": "model", "link": 274, "label": "model", "type": "MODEL", "localized_name": "model"}, {"name": "clip", "link": 275, "label": "clip", "type": "CLIP", "localized_name": "clip"}], "flags": {}, "type": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "mode": 0, "bgcolor": "#593930", "size": [413.3301086425781, 133.2881317138672], "pos": [6276.76123046875, 182.36106872558594], "id": 141, "properties": {"cnr_id": "comfy-core", "ver": "0.3.43", "widget_ue_connectable": {}, "Node name for S&R": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, "order": 5}, {"outputs": [{"name": "MODEL", "links": [293], "label": "MODEL", "type": "MODEL", "localized_name": "MODEL"}, {"name": "CLIP", "links": [294], "label": "CLIP", "type": "CLIP", "localized_name": "CLIP"}], "color": "#332922", "widgets_values": ["Wan21_T2V_14B_lightx2v_cfg_step_distill_lora_rank32.safetensors", 0.4000000000000001, 0.4000000000000001], "inputs": [{"name": "model", "link": 276, "label": "model", "type": "MODEL", "localized_name": "model"}, {"name": "clip", "link": 277, "label": "clip", "type": "CLIP", "localized_name": "clip"}], "flags": {}, "type": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "mode": 0, "bgcolor": "#593930", "size": [438.8387451171875, 140.66421508789062], "pos": [6266.76123046875, 382.3610534667969], "id": 142, "properties": {"cnr_id": "comfy-core", "ver": "0.3.43", "widget_ue_connectable": {}, "Node name for S&R": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, "order": 8}, {"outputs": [{"name": "VAE", "slot_index": 0, "links": [286, 288], "label": "VAE", "type": "VAE", "localized_name": "VAE"}], "color": "#322", "widgets_values": ["wan_2.1_vae.safetensors"], "inputs": [], "flags": {}, "type": "VAELoader", "mode": 0, "bgcolor": "#533", "size": [367.7924499511719, 70.40070343017578], "pos": [5812.04833984375, 595.082763671875], "id": 143, "properties": {"cnr_id": "comfy-core", "models": [{"name": "wan_2.1_vae.safetensors", "directory": "vae", "url": "https://huggingface.co/Comfy-Org/Wan_2.1_ComfyUI_repackaged/resolve/main/split_files/vae/wan_2.1_vae.safetensors?download=true"}], "ver": "0.3.44", "widget_ue_connectable": {}, "Node name for S&R": "VAELoader"}, "order": 0}, {"outputs": [{"name": "image1", "links": [296], "label": "image1", "type": "IMAGE", "localized_name": "image1"}], "color": "#323", "widgets_values": ["Alibaba-PuHuiTi-Heavy.ttf", 40, 8, "dark"], "inputs": [{"name": "reel_1", "link": 278, "label": "reel_1", "type": "<PERSON><PERSON>", "localized_name": "reel_1"}, {"shape": 7, "name": "reel_2", "label": "reel_2", "type": "<PERSON><PERSON>", "localized_name": "reel_2"}, {"shape": 7, "name": "reel_3", "label": "reel_3", "type": "<PERSON><PERSON>", "localized_name": "reel_3"}, {"shape": 7, "name": "reel_4", "label": "reel_4", "type": "<PERSON><PERSON>", "localized_name": "reel_4"}], "flags": {}, "type": "LayerUtility: ImageReelComposit", "mode": 0, "bgcolor": "#535", "size": [277.20001220703125, 190], "pos": [7816.85302734375, 476.2682800292969], "id": 145, "properties": {"cnr_id": "comfyui_layerstyle", "ver": "3bfe8e435d167b4a5bf08716729f89802dbbaa6f", "widget_ue_connectable": {}, "Node name for S&R": "LayerUtility: ImageReelComposit"}, "order": 22}, {"outputs": [{"name": "MODEL", "slot_index": 0, "links": [274], "label": "MODEL", "type": "MODEL", "localized_name": "MODEL"}], "color": "#223", "widgets_values": ["wan2.2_t2v_low_noise_14B_fp8_scaled.safetensors", "fp8_e4m3fn"], "inputs": [], "flags": {}, "type": "UNETLoader", "mode": 0, "bgcolor": "#335", "size": [420.1534118652344, 83.15281677246094], "pos": [5798.3583984375, 206.20188903808594], "id": 147, "properties": {"cnr_id": "comfy-core", "models": [{"name": "wan2.1_t2v_1.3B_fp16.safetensors", "directory": "diffusion_models", "url": "https://huggingface.co/Comfy-Org/Wan_2.1_ComfyUI_repackaged/resolve/main/split_files/diffusion_models/wan2.1_t2v_1.3B_fp16.safetensors?download=true"}], "ver": "0.3.44", "widget_ue_connectable": {}, "Node name for S&R": "UNETLoader"}, "order": 1}, {"outputs": [{"name": "CLIP", "slot_index": 0, "links": [275], "label": "CLIP", "type": "CLIP", "localized_name": "CLIP"}], "color": "#432", "widgets_values": ["umt5_xxl_fp8_e4m3fn_scaled.safetensors", "wan", "default"], "inputs": [], "flags": {}, "type": "CLIPLoader", "mode": 0, "bgcolor": "#653", "size": [360.6138610839844, 98], "pos": [5826.96923828125, 404.3288269042969], "id": 149, "properties": {"cnr_id": "comfy-core", "models": [{"name": "umt5_xxl_fp8_e4m3fn_scaled.safetensors", "directory": "text_encoders", "url": "https://huggingface.co/Comfy-Org/Wan_2.1_ComfyUI_repackaged/resolve/main/split_files/text_encoders/umt5_xxl_fp8_e4m3fn_scaled.safetensors?download=true"}], "ver": "0.3.44", "widget_ue_connectable": {}, "Node name for S&R": "CLIPLoader"}, "order": 2}, {"outputs": [{"name": "MODEL", "links": [289], "label": "MODEL", "type": "MODEL", "localized_name": "MODEL"}], "color": "#223", "widgets_values": ["auto"], "inputs": [{"name": "model", "link": 284, "label": "model", "type": "MODEL", "localized_name": "model"}], "flags": {}, "type": "PathchSageAttentionKJ", "mode": 0, "bgcolor": "#335", "size": [244.58575439453125, 62.97879409790039], "pos": [7253.19189453125, 307.4977722167969], "id": 150, "properties": {"widget_ue_connectable": {}, "Node name for S&R": "PathchSageAttentionKJ"}, "order": 16}, {"outputs": [{"name": "IMAGE", "links": [268, 295], "label": "IMAGE", "type": "IMAGE", "localized_name": "IMAGE"}], "color": "#323", "widgets_values": [], "inputs": [{"name": "samples", "link": 285, "label": "samples", "type": "LATENT", "localized_name": "samples"}, {"name": "vae", "link": 286, "label": "vae", "type": "VAE", "localized_name": "vae"}], "flags": {}, "type": "VAEDecode", "mode": 0, "bgcolor": "#535", "size": [246.62803649902344, 59.8025016784668], "pos": [7253.14794921875, 607.879638671875], "id": 151, "properties": {"cnr_id": "comfy-core", "ver": "0.3.43", "widget_ue_connectable": {}, "Node name for S&R": "VAEDecode"}, "order": 19}, {"outputs": [{"name": "LATENT", "links": [283], "label": "LATENT", "type": "LATENT", "localized_name": "LATENT"}], "color": "#323", "widgets_values": [], "inputs": [{"name": "pixels", "link": 287, "label": "pixels", "type": "IMAGE", "localized_name": "pixels"}, {"name": "vae", "link": 288, "label": "vae", "type": "VAE", "localized_name": "vae"}], "flags": {}, "type": "VAEEncode", "mode": 0, "bgcolor": "#535", "size": [241.07766723632812, 46], "pos": [7257.82275390625, 516.135498046875], "id": 152, "properties": {"cnr_id": "comfy-core", "ver": "0.3.44", "widget_ue_connectable": {}, "Node name for S&R": "VAEEncode"}, "order": 9}, {"outputs": [{"name": "MODEL", "slot_index": 0, "links": [280], "label": "MODEL", "type": "MODEL", "localized_name": "MODEL"}], "color": "#223", "widgets_values": [4.000000000000001], "inputs": [{"name": "model", "link": 289, "label": "model", "type": "MODEL", "localized_name": "model"}], "flags": {}, "type": "ModelSamplingSD3", "mode": 0, "bgcolor": "#335", "size": [237.0276641845703, 60.845035552978516], "pos": [7260.4072265625, 412.0114440917969], "id": 153, "properties": {"cnr_id": "comfy-core", "ver": "0.3.45", "widget_ue_connectable": {}, "Node name for S&R": "ModelSamplingSD3"}, "order": 17}, {"outputs": [{"name": "STRING", "links": [271], "label": "STRING", "type": "*", "localized_name": "STRING"}, {"name": "show_help", "label": "show_help", "type": "STRING", "localized_name": "show_help"}], "color": "#232", "widgets_values": ["，", "", ""], "inputs": [{"shape": 7, "name": "text1", "link": 290, "label": "text1", "type": "STRING"}, {"shape": 7, "name": "text2", "link": 291, "label": "text2", "type": "STRING"}], "flags": {}, "type": "CR Text Concatenate", "mode": 0, "bgcolor": "#353", "size": [343.8714904785156, 126], "pos": [5368.48583984375, 187.61888122558594], "id": 154, "properties": {"cnr_id": "comfyroll", "ver": "d78b780ae43fcf8c6b7c6505e6ffb4584281ceca", "widget_ue_connectable": {}, "Node name for S&R": "CR Text Concatenate"}, "order": 12}, {"outputs": [{"name": "MODEL", "links": [284], "label": "MODEL", "type": "MODEL", "localized_name": "MODEL"}], "color": "#323", "widgets_values": [true, 0], "inputs": [{"name": "model", "link": 292, "label": "model", "type": "MODEL", "localized_name": "model"}], "flags": {}, "type": "CFGZeroStarAndInit", "mode": 4, "bgcolor": "#535", "size": [270, 82], "pos": [7246.8671875, 185.38255310058594], "id": 155, "properties": {"cnr_id": "comfyui-kjnodes", "ver": "d9425173e77b5be8c75492a72424ddffae4d4445", "widget_ue_connectable": {}, "Node name for S&R": "CFGZeroStarAndInit"}, "order": 13}, {"outputs": [{"name": "MODEL", "links": [292], "label": "MODEL", "type": "MODEL", "localized_name": "MODEL"}, {"name": "CLIP", "links": [269, 270], "label": "CLIP", "type": "CLIP", "localized_name": "CLIP"}], "color": "#332922", "widgets_values": ["WAN2.1_SmartphoneSnapshotPhotoReality_v1_by-AI_Characters.safetensors", 0.7000000000000002, 0.7000000000000002], "inputs": [{"name": "model", "link": 293, "label": "model", "type": "MODEL", "localized_name": "model"}, {"name": "clip", "link": 294, "label": "clip", "type": "CLIP", "localized_name": "clip"}], "flags": {}, "type": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "mode": 0, "bgcolor": "#593930", "size": [434.2865295410156, 126], "pos": [6259.63720703125, 591.616943359375], "id": 156, "properties": {"cnr_id": "comfy-core", "ver": "0.3.43", "widget_ue_connectable": {}, "Node name for S&R": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, "order": 11}, {"outputs": [], "color": "#232", "widgets_values": ["ComfyUI"], "inputs": [{"name": "images", "link": 295, "label": "images", "type": "IMAGE", "localized_name": "images"}], "flags": {}, "type": "SaveImage", "mode": 0, "bgcolor": "#353", "size": [547.776611328125, 891.9474487304688], "pos": [5945.35302734375, 860.155029296875], "id": 157, "properties": {"widget_ue_connectable": {}, "Node name for S&R": "SaveImage"}, "order": 21}, {"outputs": [{"name": "text", "links": [290], "label": "text", "type": "*", "localized_name": "text"}, {"name": "show_help", "label": "show_help", "type": "STRING", "localized_name": "show_help"}], "color": "#232", "widgets_values": ["2010 年代初期用手机拍摄并上传到 Facebook 的快照照片，具有动态自然光和中性白平衡和褪色颜色，眼神忧郁"], "inputs": [], "flags": {}, "type": "CR Text", "mode": 0, "bgcolor": "#353", "size": [327.2252502441406, 113], "pos": [5000.14501953125, 189.27122497558594], "id": 138, "properties": {"cnr_id": "comfyroll", "ver": "d78b780ae43fcf8c6b7c6505e6ffb4584281ceca", "widget_ue_connectable": {}, "Node name for S&R": "CR Text"}, "order": 3}, {"outputs": [], "color": "#232", "widgets_values": ["ComfyUI"], "inputs": [{"name": "images", "link": 296, "label": "images", "type": "IMAGE", "localized_name": "images"}], "flags": {}, "type": "SaveImage", "mode": 0, "bgcolor": "#353", "size": [1128.177001953125, 873.9366455078125], "pos": [6522.5146484375, 860.477294921875], "id": 158, "properties": {"widget_ue_connectable": {}, "Node name for S&R": "SaveImage"}, "order": 23}, {"outputs": [{"name": "describe", "links": [272], "label": "describe", "type": "STRING", "localized_name": "describe"}], "color": "#223", "widgets_values": ["Please provide a detailed description of this image. If any characters in the image are familiar to you, such as celebrities, movie characters, or animated figures, please directly use their names. The description should be as detailed as possible, but should not exceed 200 words."], "inputs": [{"name": "ref_image", "link": 279, "label": "ref_image", "type": "IMAGE", "localized_name": "ref_image"}], "flags": {}, "type": "RH_Captioner", "mode": 0, "bgcolor": "#335", "size": [364.04083251953125, 196.65501403808594], "pos": [4982.45166015625, 370.6641845703125], "id": 146, "properties": {"widget_ue_connectable": {}, "Node name for S&R": "RH_Captioner"}, "order": 7}, {"outputs": [{"name": "LATENT", "links": [285], "label": "LATENT", "type": "LATENT", "localized_name": "LATENT"}], "color": "#323", "widgets_values": [29031436796287, "randomize", 20, 1, "res_2s", "bong_tangent", 0.4000000000000001], "inputs": [{"name": "model", "link": 280, "label": "model", "type": "MODEL", "localized_name": "model"}, {"name": "positive", "link": 281, "label": "positive", "type": "CONDITIONING", "localized_name": "positive"}, {"name": "negative", "link": 282, "label": "negative", "type": "CONDITIONING", "localized_name": "negative"}, {"name": "latent_image", "link": 283, "label": "latent_image", "type": "LATENT", "localized_name": "latent_image"}], "flags": {}, "type": "K<PERSON><PERSON><PERSON>", "mode": 0, "bgcolor": "#535", "size": [250, 474], "pos": [7540.8974609375, 178.08567810058594], "id": 148, "properties": {"cnr_id": "comfy-core", "ver": "0.3.43", "widget_ue_connectable": {}, "Node name for S&R": "K<PERSON><PERSON><PERSON>"}, "order": 18}, {"outputs": [{"name": "IMAGE", "links": [267, 273, 279], "label": "IMAGE", "type": "IMAGE", "localized_name": "IMAGE"}, {"name": "MASK", "label": "MASK", "type": "MASK", "localized_name": "MASK"}], "color": "#232", "widgets_values": ["80d47ccb9aa74b6714c271a3cc2a81d589fee48dc6c922145ab07b61e25f4ccb.webp", "image", ""], "inputs": [], "flags": {}, "type": "LoadImage", "mode": 0, "bgcolor": "#353", "size": [556.7559814453125, 898.8511352539062], "pos": [5363.56396484375, 856.502685546875], "id": 144, "properties": {"cnr_id": "comfy-core", "ver": "0.3.44", "widget_ue_connectable": {}, "Node name for S&R": "LoadImage"}, "order": 4}, {"outputs": [{"name": "output", "links": [291], "label": "output", "type": "*", "localized_name": "output"}], "color": "#232", "widgets_values": ["The image depicts a serene outdoor scene featuring a woman practicing a yoga pose amid a field of vibrant violets and blooming cherry blossom trees. The setting is tranquil, with soft pink blossoms in the background and a mix of earth tones and greenery on the ground. The woman is performing the plow pose (\"<PERSON><PERSON><PERSON>\") in a graceful, inverted position, with her legs stretched back over her head and her toes nearly touching the ground. She has long dark hair, tied in a low ponytail or braid. She is dressed in a white top and pink-and-black animal print shorts. A wooden chair is visible slightly behind her, adding an element of rustic simplicity to the image. The composition blends the beauty of nature with the elegance of human movement and flexibility, exuding harmony between the two. The scene is well-framed, emphasizing both the natural environment and the subject's posture."], "inputs": [{"shape": 7, "name": "anything", "link": 272, "label": "anything", "type": "*", "localized_name": "anything"}], "flags": {}, "type": "easy showAnything", "mode": 0, "bgcolor": "#353", "size": [351.6213073730469, 373.6241455078125], "pos": [5366.96630859375, 365.2985534667969], "id": 139, "properties": {"cnr_id": "comfyui-easy-use", "ver": "e7320ec0c463641f702add48b87363c7a9713a1c", "widget_ue_connectable": {}, "Node name for S&R": "easy showAnything"}, "order": 10}], "extra": {"VHS_KeepIntermediate": true, "links_added_by_ue": [], "workspace_info": {"id": "2DPvFpHLLMzmEMupNJKw3"}, "VHS_MetadataImage": true, "ue_links": [], "0246.VERSION": [0, 0, 4], "VHS_latentpreviewrate": 0, "frontendVersion": "1.23.4", "VHS_latentpreview": false, "ds": {"offset": [-5860.810347805271, -78.03171011061211], "scale": 0.7972024500000008}}, "groups": [{"color": "#3f789e", "font_size": 24, "flags": {}, "id": 6, "title": "模型", "bounding": [5760.982421875, 100.6403579711914, 970, 654]}, {"color": "#3f789e", "font_size": 24, "flags": {}, "id": 7, "title": "采样", "bounding": [6761.046875, 94.0856704711914, 1345.80615234375, 592.1827392578125]}, {"color": "#3f789e", "font_size": 24, "flags": {}, "id": 8, "title": "尺寸提示词处理", "bounding": [4652.52978515625, 94.3405532836914, 1086.057861328125, 664.5821533203125]}], "links": [[267, 144, 0, 135, 0, "IMAGE"], [268, 151, 0, 135, 1, "IMAGE"], [269, 156, 1, 136, 0, "CLIP"], [270, 156, 1, 137, 0, "CLIP"], [271, 154, 0, 137, 1, "STRING"], [272, 146, 0, 139, 0, "*"], [273, 144, 0, 140, 0, "IMAGE"], [274, 147, 0, 141, 0, "MODEL"], [275, 149, 0, 141, 1, "CLIP"], [276, 141, 0, 142, 0, "MODEL"], [277, 141, 1, 142, 1, "CLIP"], [278, 135, 0, 145, 0, "<PERSON><PERSON>"], [279, 144, 0, 146, 0, "IMAGE"], [280, 153, 0, 148, 0, "MODEL"], [281, 137, 0, 148, 1, "CONDITIONING"], [282, 136, 0, 148, 2, "CONDITIONING"], [283, 152, 0, 148, 3, "LATENT"], [284, 155, 0, 150, 0, "MODEL"], [285, 148, 0, 151, 0, "LATENT"], [286, 143, 0, 151, 1, "VAE"], [287, 140, 0, 152, 0, "IMAGE"], [288, 143, 0, 152, 1, "VAE"], [289, 150, 0, 153, 0, "MODEL"], [290, 138, 0, 154, 0, "STRING"], [291, 139, 0, 154, 1, "STRING"], [292, 156, 0, 155, 0, "MODEL"], [293, 142, 0, 156, 0, "MODEL"], [294, 142, 1, 156, 1, "CLIP"], [295, 151, 0, 157, 0, "IMAGE"], [296, 145, 0, 158, 0, "IMAGE"]], "id": "25dfce61-3571-4c9a-a7f8-6a83951d66aa", "config": {}, "version": 0.4, "last_node_id": 158, "revision": 0}