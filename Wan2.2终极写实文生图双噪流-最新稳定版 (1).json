{"id": "25dfce61-3571-4c9a-a7f8-6a83951d66aa", "revision": 0, "last_node_id": 169, "last_link_id": 261, "nodes": [{"id": 94, "type": "CLIPTextEncode", "pos": [8734.2548828125, 3741.30419921875], "size": [450, 150], "flags": {"collapsed": false}, "order": 13, "mode": 0, "inputs": [{"label": "clip", "name": "clip", "type": "CLIP", "link": 138}], "outputs": [{"label": "CONDITIONING", "name": "CONDITIONING", "type": "CONDITIONING", "links": [159, 163]}], "properties": {"Node name for S&R": "CLIPTextEncode", "cnr_id": "comfy-core", "ver": "0.3.43", "widget_ue_connectable": {}}, "widgets_values": [""], "color": "#2a363b", "bgcolor": "#3f5159"}, {"id": 95, "type": "EmptyHunyuanLatentVideo", "pos": [8744.2548828125, 3941.30322265625], "size": [450, 150], "flags": {}, "order": 9, "mode": 0, "inputs": [{"label": "width", "name": "width", "type": "INT", "widget": {"name": "width"}, "link": 139}, {"label": "height", "name": "height", "type": "INT", "widget": {"name": "height"}, "link": 140}], "outputs": [{"label": "LATENT", "name": "LATENT", "type": "LATENT", "links": [160]}], "properties": {"Node name for S&R": "EmptyHunyuanLatentVideo", "cnr_id": "comfy-core", "ver": "0.3.43", "widget_ue_connectable": {}}, "widgets_values": [1536, 1536, 1, 1], "color": "#323", "bgcolor": "#535"}, {"id": 97, "type": "PathchSageAttentionKJ", "pos": [7950.19384765625, 4309.4423828125], "size": [315, 58], "flags": {}, "order": 22, "mode": 0, "inputs": [{"label": "model", "name": "model", "type": "MODEL", "link": 142}], "outputs": [{"label": "MODEL", "name": "MODEL", "type": "MODEL", "links": [146]}], "properties": {"Node name for S&R": "PathchSageAttentionKJ", "widget_ue_connectable": {}}, "widgets_values": ["auto"], "color": "#223", "bgcolor": "#335"}, {"id": 106, "type": "CLIPTextEncode", "pos": [8726.884765625, 3330.657470703125], "size": [450, 350], "flags": {}, "order": 20, "mode": 0, "inputs": [{"label": "clip", "name": "clip", "type": "CLIP", "link": 150}, {"label": "text", "name": "text", "type": "STRING", "widget": {"name": "text"}, "link": 151}], "outputs": [{"label": "CONDITIONING", "name": "CONDITIONING", "type": "CONDITIONING", "links": [158, 162]}], "properties": {"Node name for S&R": "CLIPTextEncode", "cnr_id": "comfy-core", "ver": "0.3.43", "widget_ue_connectable": {}}, "widgets_values": [" 描绘了一位年轻女性脸部特写, 被透过薄纱窗帘的金色晨光照亮, 从侧面拍摄.\n在紧凑的1:1特写镜头中, 仅可见女性脸庞的一半, 略微转向附近的一扇窗户, 温暖, 清晨的阳光透过白色布帘倾泻而入.光线柔和地掠过她的面容, 突显出她脸颊上细腻的桃色绒毛和嘴唇附近干燥皮肤的微弱纹理.她的眼睛在画框边缘刚刚可见, 捕捉到一丝琥珀色光芒.漂浮在空气中的杂乱发丝在逆光中发光.背景模糊不清但显然是室内--可能是卧室--失焦并染上日黄色调.她表情中的轻微眯眼可见清晨的昏沉感.镜头在她脸部最亮的部分周围形成淡淡的光晕, 光线最强的区域有轻微的色彩溢出.感觉非常亲密, 像是醒来后拍下的第一张照片, 在寂静中捕捉."], "color": "#2a363b", "bgcolor": "#3f5159"}, {"id": 108, "type": "VAELoader", "pos": [7953.4365234375, 3500.140869140625], "size": [367.7924499511719, 70.40070343017578], "flags": {}, "order": 0, "mode": 0, "inputs": [], "outputs": [{"label": "VAE", "name": "VAE", "type": "VAE", "slot_index": 0, "links": [171]}], "properties": {"Node name for S&R": "VAELoader", "cnr_id": "comfy-core", "ver": "0.3.44", "models": [{"name": "wan_2.1_vae.safetensors", "url": "https://huggingface.co/Comfy-Org/Wan_2.1_ComfyUI_repackaged/resolve/main/split_files/vae/wan_2.1_vae.safetensors?download=true", "directory": "vae"}], "widget_ue_connectable": {}}, "widgets_values": ["wan_2.1_vae.safetensors"], "color": "#322", "bgcolor": "#533"}, {"id": 119, "type": "easy showAnything", "pos": [8274.138671875, 3837.375244140625], "size": [398.4926452636719, 296.8557434082031], "flags": {}, "order": 21, "mode": 0, "inputs": [{"label": "anything", "name": "anything", "shape": 7, "type": "*", "link": 173}], "outputs": [{"label": "output", "name": "output", "type": "*"}], "properties": {"Node name for S&R": "easy showAnything", "widget_ue_connectable": {}}, "widgets_values": ["2010年代初用手机拍摄并上传到Facebook的快照照片, 具有动态自然光照, 以及中性白色色彩平衡和褪色效果,女子穿烟灰色立领袄裙，上袄用暗纹锦缎制成，领口缀着七颗圆润的珍珠扣，胸前系着一条水绿色宫绦（打了个如意结）。她右手搭在朱红廊柱上，左手拢着袄裙领口，袖口露出半截皓腕，腕间系着红绳串的蜜蜡珠子。发型是低螺髻，插着一支玳瑁梳，梳齿间绕着几缕银丝。廊外雨丝斜斜飘落，水珠沾在她肩头的袄裙上，领口珍珠被湿气润得更亮，她微微侧头听雨声，鬓边碎发被风吹得轻扬。"], "color": "#232", "bgcolor": "#353"}, {"id": 98, "type": "PathchSageAttentionKJ", "pos": [7951.18310546875, 4463.15966796875], "size": [315, 58], "flags": {}, "order": 25, "mode": 0, "inputs": [{"label": "model", "name": "model", "type": "MODEL", "link": 143}], "outputs": [{"label": "MODEL", "name": "MODEL", "type": "MODEL", "links": [172]}], "properties": {"Node name for S&R": "PathchSageAttentionKJ", "widget_ue_connectable": {}}, "widgets_values": ["auto"], "color": "#223", "bgcolor": "#335"}, {"id": 131, "type": "LoraLoaderModelOnly", "pos": [7086.00146484375, 4315.12109375], "size": [434.5848083496094, 82], "flags": {}, "order": 15, "mode": 0, "inputs": [{"label": "model", "name": "model", "type": "MODEL", "link": 180}], "outputs": [{"label": "MODEL", "name": "MODEL", "type": "MODEL", "links": [181]}], "properties": {"Node name for S&R": "LoraLoaderModelOnly", "widget_ue_connectable": {}}, "widgets_values": ["Wan2.1_T2V_14B_FusionX_LoRA.safetensors", 0.4000000000000001], "color": "#223", "bgcolor": "#335"}, {"id": 103, "type": "LoraLoaderModelOnly", "pos": [7085.59716796875, 4464.43310546875], "size": [434.5848083496094, 82], "flags": {}, "order": 19, "mode": 0, "inputs": [{"label": "model", "name": "model", "type": "MODEL", "link": 183}], "outputs": [{"label": "MODEL", "name": "MODEL", "type": "MODEL", "links": [141]}], "properties": {"Node name for S&R": "LoraLoaderModelOnly", "widget_ue_connectable": {}}, "widgets_values": ["Wan2.1_T2V_14B_FusionX_LoRA.safetensors", 0.4000000000000001], "color": "#223", "bgcolor": "#335"}, {"id": 99, "type": "LoraLoaderModelOnly", "pos": [6510.19384765625, 4329.4423828125], "size": [494.0005798339844, 83.3822250366211], "flags": {}, "order": 11, "mode": 0, "inputs": [{"label": "model", "name": "model", "type": "MODEL", "link": 235}], "outputs": [{"label": "MODEL", "name": "MODEL", "type": "MODEL", "links": [180]}], "properties": {"Node name for S&R": "LoraLoaderModelOnly", "widget_ue_connectable": {}}, "widgets_values": ["Wan21_T2V_14B_lightx2v_cfg_step_distill_lora_rank32.safetensors", 0.4000000000000001], "color": "#223", "bgcolor": "#335"}, {"id": 96, "type": "CFGZeroStarAndInit", "pos": [7587.7275390625, 4462.5361328125], "size": [315, 82], "flags": {}, "order": 23, "mode": 4, "inputs": [{"label": "model", "name": "model", "type": "MODEL", "link": 141}], "outputs": [{"label": "MODEL", "name": "MODEL", "type": "MODEL", "links": [143]}], "properties": {"Node name for S&R": "CFGZeroStarAndInit", "widget_ue_connectable": {}}, "widgets_values": [true, 0], "color": "#323", "bgcolor": "#535"}, {"id": 100, "type": "LoraLoaderModelOnly", "pos": [6500.1943359375, 4479.4423828125], "size": [516.1165161132812, 82], "flags": {}, "order": 12, "mode": 0, "inputs": [{"label": "model", "name": "model", "type": "MODEL", "link": 237}], "outputs": [{"label": "MODEL", "name": "MODEL", "type": "MODEL", "links": [182]}], "properties": {"Node name for S&R": "LoraLoaderModelOnly", "widget_ue_connectable": {}}, "widgets_values": ["Wan21_T2V_14B_lightx2v_cfg_step_distill_lora_rank32.safetensors", 0.4000000000000001], "color": "#223", "bgcolor": "#335"}, {"id": 125, "type": "Note", "pos": [10020.1513671875, 3538.021240234375], "size": [270.4560546875, 88], "flags": {}, "order": 1, "mode": 0, "inputs": [], "outputs": [], "properties": {}, "widgets_values": ["🔝上面可以切换提示字类型，默认智能扩写\n1：自定义写提示词\n2：智能扩写提示词\n"], "color": "#432", "bgcolor": "#653"}, {"id": 113, "type": "KSamplerAdvanced", "pos": [8660.912109375, 4280.833984375], "size": [304.748046875, 334], "flags": {}, "order": 26, "mode": 0, "inputs": [{"label": "model", "name": "model", "type": "MODEL", "link": 157}, {"label": "positive", "name": "positive", "type": "CONDITIONING", "link": 158}, {"label": "negative", "name": "negative", "type": "CONDITIONING", "link": 159}, {"label": "latent_image", "name": "latent_image", "type": "LATENT", "link": 160}], "outputs": [{"label": "LATENT", "name": "LATENT", "type": "LATENT", "links": [164]}], "properties": {"Node name for S&R": "KSamplerAdvanced", "widget_ue_connectable": {}}, "widgets_values": ["enable", 1, "fixed", 10, 3, "res_2s", "bong_tangent", 0, 6, "enable"], "color": "#323", "bgcolor": "#535"}, {"id": 114, "type": "KSamplerAdvanced", "pos": [9023.96875, 4285.30322265625], "size": [304.748046875, 334], "flags": {}, "order": 28, "mode": 0, "inputs": [{"label": "model", "name": "model", "type": "MODEL", "link": 161}, {"label": "positive", "name": "positive", "type": "CONDITIONING", "link": 162}, {"label": "negative", "name": "negative", "type": "CONDITIONING", "link": 163}, {"label": "latent_image", "name": "latent_image", "type": "LATENT", "link": 164}], "outputs": [{"label": "LATENT", "name": "LATENT", "type": "LATENT", "links": [170]}], "properties": {"Node name for S&R": "KSamplerAdvanced", "widget_ue_connectable": {}}, "widgets_values": ["disable", 1, "fixed", 10, 1, "res_2s", "bong_tangent", 6, 10000, "disable"], "color": "#323", "bgcolor": "#535"}, {"id": 104, "type": "CFGZeroStarAndInit", "pos": [7586.220703125, 4323.7431640625], "size": [315, 82], "flags": {}, "order": 18, "mode": 4, "inputs": [{"label": "model", "name": "model", "type": "MODEL", "link": 181}], "outputs": [{"label": "MODEL", "name": "MODEL", "type": "MODEL", "links": [142]}], "properties": {"Node name for S&R": "CFGZeroStarAndInit", "widget_ue_connectable": {}}, "widgets_values": [true, 0], "color": "#323", "bgcolor": "#535"}, {"id": 126, "type": "ImpactSwitch", "pos": [10021.1591796875, 3369.331787109375], "size": [315, 122], "flags": {}, "order": 14, "mode": 0, "inputs": [{"label": "input1", "name": "input1", "shape": 7, "type": "STRING", "link": 175}, {"label": "input2", "name": "input2", "type": "STRING", "link": 179}, {"label": "input3", "name": "input3", "type": "STRING"}], "outputs": [{"label": "STRING", "name": "STRING", "type": "STRING", "links": [239]}, {"label": "selected_label", "name": "selected_label", "type": "STRING"}, {"label": "selected_index", "name": "selected_index", "type": "INT"}], "properties": {"Node name for S&R": "ImpactSwitch", "widget_ue_connectable": {}}, "widgets_values": [1, false], "color": "#223", "bgcolor": "#335"}, {"id": 122, "type": "ImpactInt", "pos": [9282.9267578125, 3385.578857421875], "size": [315, 58], "flags": {}, "order": 2, "mode": 0, "inputs": [], "outputs": [{"label": "INT", "name": "INT", "type": "INT", "links": [139]}], "properties": {"Node name for S&R": "ImpactInt", "widget_ue_connectable": {}}, "widgets_values": [1536], "color": "#223", "bgcolor": "#335"}, {"id": 123, "type": "ImpactInt", "pos": [9286.6064453125, 3560.65185546875], "size": [315, 58], "flags": {}, "order": 3, "mode": 0, "inputs": [], "outputs": [{"label": "INT", "name": "INT", "type": "INT", "links": [140]}], "properties": {"Node name for S&R": "ImpactInt", "widget_ue_connectable": {}}, "widgets_values": [1536], "color": "#223", "bgcolor": "#335"}, {"id": 124, "type": "CR Text", "pos": [9669.6025390625, 3383.221435546875], "size": [315.6014404296875, 217.5799560546875], "flags": {}, "order": 4, "mode": 0, "inputs": [], "outputs": [{"label": "text", "name": "text", "type": "*", "links": [175, 177]}, {"label": "show_help", "name": "show_help", "type": "STRING"}], "properties": {"Node name for S&R": "CR Text", "widget_ue_connectable": {}}, "widgets_values": ["女子穿烟灰色立领袄裙，上袄用暗纹锦缎制成，领口缀着七颗圆润的珍珠扣，胸前系着一条水绿色宫绦（打了个如意结）。她右手搭在朱红廊柱上，左手拢着袄裙领口，袖口露出半截皓腕，腕间系着红绳串的蜜蜡珠子。发型是低螺髻，插着一支玳瑁梳，梳齿间绕着几缕银丝。廊外雨丝斜斜飘落，水珠沾在她肩头的袄裙上，领口珍珠被湿气润得更亮，她微微侧头听雨声，鬓边碎发被风吹得轻扬。"], "color": "#223", "bgcolor": "#335"}, {"id": 136, "type": "UNETLoader", "pos": [5506.5, 4281.00537109375], "size": [430, 82], "flags": {}, "order": 5, "mode": 0, "inputs": [], "outputs": [{"label": "MODEL", "name": "MODEL", "type": "MODEL", "slot_index": 0, "links": [235]}], "properties": {"Node name for S&R": "UNETLoader", "widget_ue_connectable": {}}, "widgets_values": ["wan2.2_t2v_high_noise_14B_fp8_scaled.safetensors", "fp8_e4m3fn"], "color": "#223", "bgcolor": "#335"}, {"id": 135, "type": "UNETLoader", "pos": [5519.33984375, 4458.36083984375], "size": [420.1534118652344, 83.15281677246094], "flags": {}, "order": 6, "mode": 0, "inputs": [], "outputs": [{"label": "MODEL", "name": "MODEL", "type": "MODEL", "slot_index": 0, "links": [237]}], "properties": {"Node name for S&R": "UNETLoader", "cnr_id": "comfy-core", "ver": "0.3.44", "models": [{"name": "wan2.1_t2v_1.3B_fp16.safetensors", "url": "https://huggingface.co/Comfy-Org/Wan_2.1_ComfyUI_repackaged/resolve/main/split_files/diffusion_models/wan2.1_t2v_1.3B_fp16.safetensors?download=true", "directory": "diffusion_models"}], "widget_ue_connectable": {}}, "widgets_values": ["wan2.2_t2v_low_noise_14B_fp8_scaled.safetensors", "fp8_e4m3fn"], "color": "#223", "bgcolor": "#335"}, {"id": 101, "type": "ModelSamplingSD3", "pos": [8310.193359375, 4299.4423828125], "size": [210, 58], "flags": {}, "order": 24, "mode": 0, "inputs": [{"label": "model", "name": "model", "type": "MODEL", "link": 146}], "outputs": [{"label": "MODEL", "name": "MODEL", "type": "MODEL", "slot_index": 0, "links": [157]}], "properties": {"Node name for S&R": "ModelSamplingSD3", "cnr_id": "comfy-core", "ver": "0.3.45", "widget_ue_connectable": {}}, "widgets_values": [4.000000000000001], "color": "#223", "bgcolor": "#335"}, {"id": 118, "type": "ModelSamplingSD3", "pos": [8308.62890625, 4456.31591796875], "size": [210, 58], "flags": {}, "order": 27, "mode": 0, "inputs": [{"label": "model", "name": "model", "type": "MODEL", "link": 172}], "outputs": [{"label": "MODEL", "name": "MODEL", "type": "MODEL", "slot_index": 0, "links": [161]}], "properties": {"Node name for S&R": "ModelSamplingSD3", "cnr_id": "comfy-core", "ver": "0.3.45", "widget_ue_connectable": {}}, "widgets_values": [4.000000000000001], "color": "#223", "bgcolor": "#335"}, {"id": 117, "type": "VAEDecode", "pos": [9403.0771484375, 4326.759765625], "size": [171.85336303710938, 46], "flags": {"collapsed": false}, "order": 29, "mode": 0, "inputs": [{"label": "samples", "name": "samples", "type": "LATENT", "link": 170}, {"label": "vae", "name": "vae", "type": "VAE", "link": 171}], "outputs": [{"label": "IMAGE", "name": "IMAGE", "type": "IMAGE", "links": [169]}], "properties": {"Node name for S&R": "VAEDecode", "cnr_id": "comfy-core", "ver": "0.3.43", "widget_ue_connectable": {}}, "widgets_values": [], "color": "#323", "bgcolor": "#535"}, {"id": 107, "type": "CR Text Concatenate", "pos": [8381.3916015625, 3605.982177734375], "size": [315, 126], "flags": {}, "order": 17, "mode": 0, "inputs": [{"label": "text1", "name": "text1", "shape": 7, "type": "STRING", "widget": {"name": "text1"}, "link": 152}, {"label": "text2", "name": "text2", "shape": 7, "type": "STRING", "widget": {"name": "text2"}, "link": 239}], "outputs": [{"label": "STRING", "name": "STRING", "type": "*", "links": [151, 173]}, {"label": "show_help", "name": "show_help", "type": "STRING"}], "properties": {"Node name for S&R": "CR Text Concatenate", "widget_ue_connectable": {}}, "widgets_values": ["", "", ""], "color": "#232", "bgcolor": "#353"}, {"id": 120, "type": "CR Text", "pos": [7958.81591796875, 3646.954833984375], "size": [401.5205078125, 119.41521453857422], "flags": {}, "order": 7, "mode": 0, "inputs": [], "outputs": [{"label": "text", "name": "text", "type": "*", "links": [152]}, {"label": "show_help", "name": "show_help", "type": "STRING"}], "properties": {"Node name for S&R": "CR Text", "widget_ue_connectable": {}}, "widgets_values": ["2010年代初用手机拍摄并上传到Facebook的快照照片, 具有动态自然光照, 以及中性白色色彩平衡和褪色效果,"], "color": "#232", "bgcolor": "#353"}, {"id": 109, "type": "CLIPLoader", "pos": [7941.1552734375, 3331.58447265625], "size": [360.6138610839844, 98], "flags": {}, "order": 8, "mode": 0, "inputs": [], "outputs": [{"label": "CLIP", "name": "CLIP", "type": "CLIP", "slot_index": 0, "links": [138, 150]}], "properties": {"Node name for S&R": "CLIPLoader", "cnr_id": "comfy-core", "ver": "0.3.44", "models": [{"name": "umt5_xxl_fp8_e4m3fn_scaled.safetensors", "url": "https://huggingface.co/Comfy-Org/Wan_2.1_ComfyUI_repackaged/resolve/main/split_files/text_encoders/umt5_xxl_fp8_e4m3fn_scaled.safetensors?download=true", "directory": "text_encoders"}], "widget_ue_connectable": {}}, "widgets_values": ["umt5_xxl_fp8_e4m3fn_scaled.safetensors", "wan", "default"], "color": "#432", "bgcolor": "#653"}, {"id": 128, "type": "RH_LLMAPI_NODE", "pos": [7940.900390625, 3843.456298828125], "size": [274.411865234375, 286], "flags": {}, "order": 10, "mode": 0, "inputs": [{"label": "ref_image", "name": "ref_image", "shape": 7, "type": "IMAGE"}, {"label": "prompt", "name": "prompt", "type": "STRING", "widget": {"name": "prompt"}, "link": 177}], "outputs": [{"label": "describe", "name": "describe", "type": "STRING", "links": [179]}], "properties": {"Node name for S&R": "RH_LLMAPI_NODE", "widget_ue_connectable": {}}, "widgets_values": ["", "", "", "# FLUX Prompt 反推提示词说明\n\n## 🧠 角色定位\n你是一位富有艺术感的 **FLUX Prompt 助理**，具备对图像的深度理解能力，能够将视觉内容转化为丰富、生动、具体的文本提示词（prompt），以用于图像生成模型 FLUX 或 Stable Diffusion。\n\n---\n\n## 🎯 核心任务\n\n我将提供一张图片或一个简短的主题描述，你的任务是：\n\n1. **理解图像/主题**：分析图像中的内容、元素、情感与风格。\n2. **生成 Prompt**：根据分析结果，输出一段详尽的英文 prompt，用于高质量图像生成。\n\n---\n\n## 🖼️ 图像分析维度\n\n请从以下角度描述图像内容，确保 prompt 丰富、准确、具象：\n\n- **主要元素**：人物、动物、物体、风景等核心对象\n- **画面细节**：颜色、纹理、光影、服饰、姿态、动作、表情、环境构成等（不少于5处具体细节）\n- **场景氛围**：温馨、神秘、奇幻、宁静、末世感等\n- **艺术风格**：现实主义、赛博朋克、油画风、水彩、卡通、像素风、未来主义等\n- **构图视角**：如“俯视”、“仰视”、“特写”、“广角”等\n\n---\n\n## ✏️ Prompt 输出格式要求\n\n- **语言**：仅使用中文生成 prompt\n- **语气**：描述性强、画面感明确，避免口语化或模糊措辞\n- **结构**：连贯自然，不分条目，形成一段完整描述\n- **长度**：足够详尽，建议不少于60词\n- **内容限制**：\n  - 不解释 prompt 内容\n  - 不添加“生成提示词”、“Prompt:”等前缀\n\n---\n\n## ✅ 示例\n\n- **输入主题**：一只飞在雪山上的龙\n- **输出 prompt**：\n\n  > 一条雄伟的绿鳞巨龙，眼中泛着琥珀色光芒，双翼张开，飞翔在令人叹为观止的雪山群中。它强壮的身影投下长长的阴影，笼罩着高耸入云的山峰。下方是一条清澈的河流，在深谷中蜿蜒流淌，倒映着明亮的天空。空气中弥漫着飘渺的薄雾，营造出清新而梦幻的氛围。这幅画面展现了令人敬畏的自然与野性之美。", "", 0.6, 1371, "randomize"], "color": "#232", "bgcolor": "#353"}, {"id": 116, "type": "SaveImage", "pos": [9685.671875, 4265.275390625], "size": [750.646484375, 718.2611694335938], "flags": {}, "order": 30, "mode": 0, "inputs": [{"label": "images", "name": "images", "type": "IMAGE", "link": 169}], "outputs": [], "properties": {"Node name for S&R": "SaveImage", "widget_ue_connectable": {}}, "widgets_values": ["ComfyUI", ""], "color": "#323", "bgcolor": "#535"}, {"id": 130, "type": "LoraLoaderModelOnly", "pos": [6052.24267578125, 4381.3466796875], "size": [434.5848083496094, 82], "flags": {}, "order": 16, "mode": 0, "inputs": [{"label": "model", "name": "model", "type": "MODEL", "link": 182}], "outputs": [{"label": "MODEL", "name": "MODEL", "type": "MODEL", "links": [183]}], "properties": {"Node name for S&R": "LoraLoaderModelOnly", "widget_ue_connectable": {}}, "widgets_values": ["WAN2.1_SmartphoneSnapshotPhotoReality_v1_by-AI_Characters.safetensors", 0.7000000000000002], "color": "#223", "bgcolor": "#335"}], "links": [[138, 109, 0, 94, 0, "CLIP"], [139, 122, 0, 95, 0, "INT"], [140, 123, 0, 95, 1, "INT"], [141, 103, 0, 96, 0, "MODEL"], [142, 104, 0, 97, 0, "MODEL"], [143, 96, 0, 98, 0, "MODEL"], [146, 97, 0, 101, 0, "MODEL"], [150, 109, 0, 106, 0, "CLIP"], [151, 107, 0, 106, 1, "STRING"], [152, 120, 0, 107, 0, "STRING"], [157, 101, 0, 113, 0, "MODEL"], [158, 106, 0, 113, 1, "CONDITIONING"], [159, 94, 0, 113, 2, "CONDITIONING"], [160, 95, 0, 113, 3, "LATENT"], [161, 118, 0, 114, 0, "MODEL"], [162, 106, 0, 114, 1, "CONDITIONING"], [163, 94, 0, 114, 2, "CONDITIONING"], [164, 113, 0, 114, 3, "LATENT"], [169, 117, 0, 116, 0, "IMAGE"], [170, 114, 0, 117, 0, "LATENT"], [171, 108, 0, 117, 1, "VAE"], [172, 98, 0, 118, 0, "MODEL"], [173, 107, 0, 119, 0, "*"], [175, 124, 0, 126, 0, "STRING"], [177, 124, 0, 128, 1, "STRING"], [179, 128, 0, 126, 1, "STRING"], [180, 99, 0, 131, 0, "MODEL"], [181, 131, 0, 104, 0, "MODEL"], [182, 100, 0, 130, 0, "MODEL"], [183, 130, 0, 103, 0, "MODEL"], [235, 136, 0, 99, 0, "MODEL"], [237, 135, 0, 100, 0, "MODEL"], [239, 126, 0, 107, 1, "STRING"]], "groups": [{"id": 9, "title": "B站、YouTube、公众号：嘟嘟AI绘画趣味学", "bounding": [7926.2783203125, 3025.370849609375, 2362.104248046875, 159.00135803222656], "color": "#b06634", "font_size": 120, "flags": {}}, {"id": 10, "title": "入参", "bounding": [9251.8876953125, 3253.50146484375, 1144.2337646484375, 394.7895202636719], "color": "#3f789e", "font_size": 24, "flags": {}}, {"id": 11, "title": "Group", "bounding": [7914.25439453125, 3251.30517578125, 1285.0025634765625, 914.3989868164062], "color": "#3f789e", "font_size": 24, "flags": {}}, {"id": 12, "title": "模型加载", "bounding": [6030.1953125, 4229.4423828125, 2510.99462890625, 358.1201477050781], "color": "#8A8", "font_size": 24, "flags": {}}, {"id": 14, "title": "设置宽", "bounding": [9262.9267578125, 3301.57861328125, 355, 162], "color": "#3f789e", "font_size": 24, "flags": {}}, {"id": 15, "title": "设置宽", "bounding": [9266.6064453125, 3476.65185546875, 355, 162], "color": "#3f789e", "font_size": 24, "flags": {}}, {"id": 16, "title": "填写提示词", "bounding": [9653.0224609375, 3308.013671875, 340.200927734375, 314.978271484375], "color": "#3f789e", "font_size": 24, "flags": {}}], "config": {}, "extra": {"ds": {"scale": 0.4950000000000043, "offset": [-7320.8279862346535, -3339.3477825863006]}, "VHS_KeepIntermediate": true, "links_added_by_ue": [], "VHS_MetadataImage": true, "ue_links": [], "0246.VERSION": [0, 0, 4], "VHS_latentpreviewrate": 0, "frontendVersion": "1.23.4", "VHS_latentpreview": false}, "version": 0.4}