{"id": "cd63db57-98b6-43ff-8520-eaa59e21cde9", "revision": 0, "last_node_id": 52, "last_link_id": 60, "nodes": [{"id": 2, "type": "DownloadAndLoadTransNetModel", "pos": [2680.915283203125, 3125.587890625], "size": [271.0503845214844, 82], "flags": {}, "order": 0, "mode": 0, "inputs": [], "outputs": [{"label": "TransNet_model", "name": "TransNet_model", "type": "TRANSNET_MODEL", "links": [1]}], "properties": {"Node name for S&R": "DownloadAndLoadTransNetModel", "aux_id": "miao<PERSON><PERSON>ai/ComfyUI-Video-Segmentation", "ver": "89db43013f0cc9b8a3ce2f0b6fd04c2a84690e1a", "widget_ue_connectable": {}}, "widgets_values": ["transnetv2-pytorch-weights", "auto"]}, {"id": 13, "type": "BatchCount+", "pos": [3535.222900390625, 3273.191162109375], "size": [140.06015014648438, 26], "flags": {}, "order": 9, "mode": 0, "inputs": [{"label": "batch", "name": "batch", "type": "*", "link": 11}], "outputs": [{"label": "INT", "name": "INT", "type": "INT", "links": [12]}], "properties": {"Node name for S&R": "BatchCount+", "cnr_id": "comfyui_essentials", "ver": "1.1.0", "widget_ue_connectable": {}}, "widgets_values": []}, {"id": 16, "type": "MathExpression|pysssss", "pos": [4167.0166015625, 3167.238037109375], "size": [400, 201.30807495117188], "flags": {"collapsed": true}, "order": 15, "mode": 0, "inputs": [{"label": "a", "name": "a", "shape": 7, "type": "INT,FLOAT,IMAGE,LATENT", "link": 16}, {"label": "b", "name": "b", "shape": 7, "type": "INT,FLOAT,IMAGE,LATENT"}, {"label": "c", "name": "c", "shape": 7, "type": "INT,FLOAT,IMAGE,LATENT"}], "outputs": [{"label": "INT", "name": "INT", "type": "INT", "links": [41]}, {"label": "FLOAT", "name": "FLOAT", "type": "FLOAT"}], "properties": {"Node name for S&R": "MathExpression|pysssss", "cnr_id": "comfyui-custom-scripts", "ver": "1.2.5", "widget_ue_connectable": {}}, "widgets_values": ["a+1"]}, {"id": 35, "type": "DualCLIPLoader", "pos": [4640.80078125, 3323.115478515625], "size": [315, 130], "flags": {}, "order": 1, "mode": 0, "inputs": [], "outputs": [{"label": "CLIP", "name": "CLIP", "type": "CLIP", "slot_index": 0, "links": [33, 52]}], "properties": {"Node name for S&R": "DualCLIPLoader", "cnr_id": "comfy-core", "ver": "0.3.27", "widget_ue_connectable": {"clip_name1": true, "clip_name2": true, "type": true, "device": true}}, "widgets_values": ["t5xxl_fp16.safetensors", "clip_l.safetensors", "flux", "default"]}, {"id": 37, "type": "VAELoader", "pos": [4646.04443359375, 3507.5927734375], "size": [270, 58], "flags": {}, "order": 2, "mode": 0, "inputs": [], "outputs": [{"label": "VAE", "name": "VAE", "type": "VAE", "links": [35, 45]}], "properties": {"Node name for S&R": "VAELoader", "cnr_id": "comfy-core", "ver": "0.3.44", "widget_ue_connectable": {}}, "widgets_values": ["ae.safetensors"]}, {"id": 47, "type": "CLIPTextEncode", "pos": [5067.45947265625, 3581.56103515625], "size": [400, 200], "flags": {"collapsed": true}, "order": 6, "mode": 0, "inputs": [{"label": "clip", "name": "clip", "type": "CLIP", "link": 52}], "outputs": [{"label": "CONDITIONING", "name": "CONDITIONING", "type": "CONDITIONING", "links": [53]}], "properties": {"Node name for S&R": "CLIPTextEncode", "cnr_id": "comfy-core", "ver": "0.3.44", "widget_ue_connectable": {}}, "widgets_values": [""]}, {"id": 46, "type": "CLIPTextEncode", "pos": [5073.603515625, 3305.367431640625], "size": [400, 200], "flags": {}, "order": 12, "mode": 0, "inputs": [{"label": "clip", "name": "clip", "type": "CLIP", "link": 49}], "outputs": [{"label": "CONDITIONING", "name": "CONDITIONING", "type": "CONDITIONING", "links": [50]}], "properties": {"Node name for S&R": "CLIPTextEncode", "cnr_id": "comfy-core", "ver": "0.3.44", "widget_ue_connectable": {}}, "widgets_values": ["Convert to Ghibli style"]}, {"id": 36, "type": "VAEDecode", "pos": [6136.15625, 3217.9951171875], "size": [140, 46], "flags": {}, "order": 24, "mode": 0, "inputs": [{"label": "samples", "name": "samples", "type": "LATENT", "link": 34}, {"label": "vae", "name": "vae", "type": "VAE", "link": 35}], "outputs": [{"label": "IMAGE", "name": "IMAGE", "type": "IMAGE", "links": [36, 42]}], "properties": {"Node name for S&R": "VAEDecode", "cnr_id": "comfy-core", "ver": "0.3.44", "widget_ue_connectable": {}}, "widgets_values": []}, {"id": 38, "type": "PreviewImage", "pos": [6130.22021484375, 3327.5556640625], "size": [140, 246], "flags": {}, "order": 25, "mode": 0, "inputs": [{"label": "images", "name": "images", "type": "IMAGE", "link": 36}], "outputs": [], "properties": {"Node name for S&R": "PreviewImage", "cnr_id": "comfy-core", "ver": "0.3.44", "widget_ue_connectable": {}}, "widgets_values": []}, {"id": 22, "type": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "pos": [5079.359375, 3101.697509765625], "size": [270, 126], "flags": {}, "order": 8, "mode": 0, "inputs": [{"label": "model", "name": "model", "type": "MODEL", "link": 21}, {"label": "clip", "name": "clip", "type": "CLIP", "link": 33}], "outputs": [{"label": "MODEL", "name": "MODEL", "type": "MODEL", "links": [58]}, {"label": "CLIP", "name": "CLIP", "type": "CLIP", "links": [49]}], "properties": {"Node name for S&R": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "cnr_id": "comfy-core", "ver": "0.3.44", "widget_ue_connectable": {}}, "widgets_values": ["ghibliStyle_kontext_byJaneB.safetensors", 1, 1]}, {"id": 14, "type": "Int", "pos": [3525.783447265625, 3115.599609375], "size": [270, 58], "flags": {}, "order": 3, "mode": 0, "inputs": [], "outputs": [{"label": "INT", "name": "INT", "type": "INT", "links": [13]}], "properties": {"Node name for S&R": "Int", "cnr_id": "ComfyLiterals", "ver": "bdddb08ca82d90d75d97b1d437a652e0284a32ac", "widget_ue_connectable": {}}, "widgets_values": ["0"]}, {"id": 1, "type": "TransNetV2_Run", "pos": [3149.802978515625, 3139.51025390625], "size": [275.314453125, 143.7307586669922], "flags": {}, "order": 7, "mode": 0, "inputs": [{"label": "TransNet_model", "name": "TransNet_model", "type": "TRANSNET_MODEL", "link": 1}, {"label": "video", "name": "video", "shape": 7, "type": "VIDEO", "link": 5}], "outputs": [{"label": "segment_paths", "name": "segment_paths", "type": "LIST", "links": [6, 11]}, {"label": "path_string", "name": "path_string", "type": "STRING", "links": [9]}], "properties": {"Node name for S&R": "TransNetV2_Run", "aux_id": "miao<PERSON><PERSON>ai/ComfyUI-Video-Segmentation", "ver": "89db43013f0cc9b8a3ce2f0b6fd04c2a84690e1a", "widget_ue_connectable": {}}, "widgets_values": [0.5, 30, "ComfyUI\\output"]}, {"id": 7, "type": "LoadVideo", "pos": [2656.524169921875, 3268.23876953125], "size": [378.080078125, 292.12310791015625], "flags": {}, "order": 4, "mode": 0, "inputs": [], "outputs": [{"label": "VIDEO", "name": "VIDEO", "type": "VIDEO", "links": [5]}], "properties": {"Node name for S&R": "LoadVideo", "cnr_id": "comfy-core", "ver": "0.3.44", "widget_ue_connectable": {}}, "widgets_values": ["8f2b0e768f333cfa23cd347709b9874f6b2721b4c1688dfa8b3a74e68b007a49.mp4", "image"]}, {"id": 11, "type": "ShowText|pysssss", "pos": [3078.59375, 3370.42138671875], "size": [375.5548400878906, 172.94822692871094], "flags": {}, "order": 10, "mode": 0, "inputs": [{"label": "text", "name": "text", "type": "STRING", "link": 9}], "outputs": [{"label": "STRING", "name": "STRING", "shape": 6, "type": "STRING"}], "properties": {"Node name for S&R": "ShowText|pysssss", "cnr_id": "comfyui-custom-scripts", "ver": "1.2.5", "widget_ue_connectable": {}}, "widgets_values": ["D:\\ComfyUI_windows_portable\\ComfyUI\\output\\segment_001.mp4\nD:\\ComfyUI_windows_portable\\ComfyUI\\output\\segment_002.mp4\nD:\\ComfyUI_windows_portable\\ComfyUI\\output\\segment_003.mp4\nD:\\ComfyUI_windows_portable\\ComfyUI\\output\\segment_004.mp4\nD:\\ComfyUI_windows_portable\\ComfyUI\\output\\segment_005.mp4\nD:\\ComfyUI_windows_portable\\ComfyUI\\output\\segment_006.mp4\nD:\\ComfyUI_windows_portable\\ComfyUI\\output\\segment_007.mp4\nD:\\ComfyUI_windows_portable\\ComfyUI\\output\\segment_008.mp4\nD:\\ComfyUI_windows_portable\\ComfyUI\\output\\segment_009.mp4\nD:\\ComfyUI_windows_portable\\ComfyUI\\output\\segment_010.mp4\nD:\\ComfyUI_windows_portable\\ComfyUI\\output\\segment_011.mp4\nD:\\ComfyUI_windows_portable\\ComfyUI\\output\\segment_012.mp4\nD:\\ComfyUI_windows_portable\\ComfyUI\\output\\segment_013.mp4\nD:\\ComfyUI_windows_portable\\ComfyUI\\output\\segment_014.mp4\nD:\\ComfyUI_windows_portable\\ComfyUI\\output\\segment_015.mp4\nD:\\ComfyUI_windows_portable\\ComfyUI\\output\\segment_016.mp4\nD:\\ComfyUI_windows_portable\\ComfyUI\\output\\segment_017.mp4\nD:\\ComfyUI_windows_portable\\ComfyUI\\output\\segment_018.mp4\nD:\\ComfyUI_windows_portable\\ComfyUI\\output\\segment_019.mp4\nD:\\ComfyUI_windows_portable\\ComfyUI\\output\\segment_020.mp4\nD:\\ComfyUI_windows_portable\\ComfyUI\\output\\segment_021.mp4\nD:\\ComfyUI_windows_portable\\ComfyUI\\output\\segment_022.mp4\nD:\\ComfyUI_windows_portable\\ComfyUI\\output\\segment_023.mp4\nD:\\ComfyUI_windows_portable\\ComfyUI\\output\\segment_024.mp4\nD:\\ComfyUI_windows_portable\\ComfyUI\\output\\segment_025.mp4\nD:\\ComfyUI_windows_portable\\ComfyUI\\output\\segment_026.mp4\nD:\\ComfyUI_windows_portable\\ComfyUI\\output\\segment_027.mp4\nD:\\ComfyUI_windows_portable\\ComfyUI\\output\\segment_028.mp4\nD:\\ComfyUI_windows_portable\\ComfyUI\\output\\segment_029.mp4\nD:\\ComfyUI_windows_portable\\ComfyUI\\output\\segment_030.mp4", "/workspace/ComfyUI/ComfyUI\\output/segment_001.mp4\n/workspace/ComfyUI/ComfyUI\\output/segment_002.mp4\n/workspace/ComfyUI/ComfyUI\\output/segment_003.mp4"]}, {"id": 8, "type": "SelectVideo", "pos": [3526.17138671875, 3372.219482421875], "size": [270, 58], "flags": {}, "order": 14, "mode": 0, "inputs": [{"label": "segment_paths", "name": "segment_paths", "shape": 7, "type": "LIST", "link": 6}, {"label": "index", "name": "index", "type": "INT", "widget": {"name": "index"}, "link": 14}], "outputs": [{"label": "selected_path", "name": "selected_path", "type": "STRING", "links": [7]}], "properties": {"Node name for S&R": "SelectVideo", "aux_id": "miao<PERSON><PERSON>ai/ComfyUI-Video-Segmentation", "ver": "0cd41c8736b3a7bacf6403a753171c2d3f93714c", "widget_ue_connectable": {}}, "widgets_values": [1]}, {"id": 12, "type": "easy forLoopStart", "pos": [3865.13916015625, 3176.40380859375], "size": [270, 158], "flags": {}, "order": 13, "mode": 0, "inputs": [{"label": "initial_value1", "name": "initial_value1", "shape": 7, "type": "*", "link": 13}, {"label": "total", "name": "total", "type": "INT", "widget": {"name": "total"}, "link": 12}, {"label": "initial_value2", "name": "initial_value2", "type": "*"}, {"label": "initial_value3", "name": "initial_value3", "type": "*"}, {"label": "initial_value4", "name": "initial_value4", "type": "*", "link": null}], "outputs": [{"label": "flow", "name": "flow", "shape": 5, "type": "FLOW_CONTROL", "links": [38]}, {"label": "index", "name": "index", "type": "INT", "links": [14]}, {"label": "value1", "name": "value1", "type": "*", "links": [16]}, {"label": "value2", "name": "value2", "type": "*", "links": [43]}, {"label": "value3", "name": "value3", "type": "*"}, {"label": "value4", "name": "value4", "type": "*", "links": null}], "properties": {"Node name for S&R": "easy forLoopStart", "cnr_id": "comfyui-easy-use", "ver": "1.3.0", "widget_ue_connectable": {}}, "widgets_values": [1], "color": "#223", "bgcolor": "#335"}, {"id": 9, "type": "VHS_LoadVideoPath", "pos": [3718.5576171875, 3482.583984375], "size": [237.7138671875, 306], "flags": {}, "order": 16, "mode": 0, "inputs": [{"label": "meta_batch", "name": "meta_batch", "shape": 7, "type": "VHS_BatchManager"}, {"label": "vae", "name": "vae", "shape": 7, "type": "VAE"}, {"label": "video", "name": "video", "type": "STRING", "widget": {"name": "video"}, "link": 7}], "outputs": [{"label": "IMAGE", "name": "IMAGE", "type": "IMAGE", "links": [19]}, {"label": "frame_count", "name": "frame_count", "type": "INT"}, {"label": "audio", "name": "audio", "type": "AUDIO"}, {"label": "video_info", "name": "video_info", "type": "VHS_VIDEOINFO"}, {"label": "video_info", "name": "video_info", "type": "VHS_VIDEOINFO"}], "properties": {"Node name for S&R": "VHS_LoadVideoPath", "cnr_id": "comfyui-videohelpersuite", "ver": "1.6.1", "widget_ue_connectable": {}}, "widgets_values": {"video": "", "force_rate": 0, "custom_width": 0, "custom_height": 0, "frame_load_cap": 0, "skip_first_frames": 0, "select_every_nth": 1, "format": "AnimateDiff", "videopreview": {"paused": false, "hidden": false, "params": {"custom_height": 0, "filename": "", "force_rate": 0, "custom_width": 0, "select_every_nth": 1, "frame_load_cap": 0, "format": "video/", "skip_first_frames": 0, "type": "path"}}}}, {"id": 52, "type": "SaveImage", "pos": [6783.00390625, 3227.94091796875], "size": [315, 58], "flags": {}, "order": 29, "mode": 0, "inputs": [{"label": "images", "name": "images", "type": "IMAGE", "link": 60}], "outputs": [], "properties": {"Node name for S&R": "SaveImage", "widget_ue_connectable": {}}, "widgets_values": ["ComfyUI"]}, {"id": 40, "type": "easy forLoopEnd", "pos": [6432.3603515625, 3230.748291015625], "size": [178, 106], "flags": {}, "order": 27, "mode": 0, "inputs": [{"label": "flow", "name": "flow", "shape": 5, "type": "FLOW_CONTROL", "link": 38}, {"label": "initial_value1", "name": "initial_value1", "shape": 7, "type": "*", "link": 41}, {"label": "initial_value2", "name": "initial_value2", "type": "*", "link": 40}, {"label": "initial_value3", "name": "initial_value3", "type": "*"}, {"label": "initial_value4", "name": "initial_value4", "type": "*", "link": null}], "outputs": [{"label": "value1", "name": "value1", "type": "*"}, {"label": "value2", "name": "value2", "type": "*", "links": [44, 60]}, {"label": "value3", "name": "value3", "type": "*"}, {"label": "value4", "name": "value4", "type": "*", "links": null}], "properties": {"Node name for S&R": "easy forLoopEnd", "cnr_id": "comfyui-easy-use", "ver": "1.3.0", "widget_ue_connectable": {}}, "widgets_values": [], "color": "#223", "bgcolor": "#335"}, {"id": 39, "type": "easy batchAnything", "pos": [6440.380859375, 3130.959716796875], "size": [140, 46], "flags": {}, "order": 26, "mode": 0, "inputs": [{"label": "any_1", "name": "any_1", "type": "*", "link": 43}, {"label": "any_2", "name": "any_2", "type": "*", "link": 42}], "outputs": [{"label": "batch", "name": "batch", "type": "*", "links": [40]}], "properties": {"Node name for S&R": "easy batchAnything", "cnr_id": "comfyui-easy-use", "ver": "1.3.0", "widget_ue_connectable": {}}, "widgets_values": []}, {"id": 41, "type": "PreviewImage", "pos": [6429.8974609375, 3376.698974609375], "size": [274.53302001953125, 270.3890380859375], "flags": {}, "order": 28, "mode": 0, "inputs": [{"label": "images", "name": "images", "type": "IMAGE", "link": 44}], "outputs": [], "properties": {"Node name for S&R": "PreviewImage", "cnr_id": "comfy-core", "ver": "0.3.44", "widget_ue_connectable": {}}, "widgets_values": []}, {"id": 20, "type": "PreviewImage", "pos": [4302.81201171875, 3497.74658203125], "size": [235.16574096679688, 311.02001953125], "flags": {}, "order": 18, "mode": 0, "inputs": [{"label": "images", "name": "images", "type": "IMAGE", "link": 20}], "outputs": [], "properties": {"Node name for S&R": "PreviewImage", "cnr_id": "comfy-core", "ver": "0.3.44", "widget_ue_connectable": {}}, "widgets_values": []}, {"id": 29, "type": "JWImageResizeByLongerSide", "pos": [4246.92138671875, 3323.177734375], "size": [283.5611267089844, 82], "flags": {}, "order": 19, "mode": 0, "inputs": [{"label": "image", "name": "image", "type": "IMAGE", "link": 30}], "outputs": [{"label": "IMAGE", "name": "IMAGE", "type": "IMAGE", "links": [31]}], "properties": {"Node name for S&R": "JWImageResizeByLongerSide", "cnr_id": "comfyui-various", "ver": "5bd85aaf7616878471469c4ec7e11bbd0cef3bf2", "widget_ue_connectable": {}}, "widgets_values": [1024, "bicubic"]}, {"id": 18, "type": "ImageFromBatch", "pos": [3981.153076171875, 3554.431640625], "size": [270, 82], "flags": {}, "order": 17, "mode": 0, "inputs": [{"label": "image", "name": "image", "type": "IMAGE", "link": 19}], "outputs": [{"label": "IMAGE", "name": "IMAGE", "type": "IMAGE", "links": [20, 30]}], "properties": {"Node name for S&R": "ImageFromBatch", "cnr_id": "comfy-core", "ver": "0.3.42", "widget_ue_connectable": {"length": true, "batch_index": true}}, "widgets_values": [1, 1]}, {"id": 45, "type": "FluxGuidance", "pos": [5506.5009765625, 3519.000732421875], "size": [270, 58], "flags": {}, "order": 22, "mode": 0, "inputs": [{"label": "conditioning", "name": "conditioning", "type": "CONDITIONING", "link": 48}], "outputs": [{"label": "CONDITIONING", "name": "CONDITIONING", "type": "CONDITIONING", "links": [51]}], "properties": {"Node name for S&R": "FluxGuidance", "cnr_id": "comfy-core", "ver": "0.3.44", "widget_ue_connectable": {}}, "widgets_values": [2.5]}, {"id": 44, "type": "ReferenceLatent", "pos": [5509.08251953125, 3401.33740234375], "size": [211.60000610351562, 46], "flags": {}, "order": 21, "mode": 0, "inputs": [{"label": "conditioning", "name": "conditioning", "type": "CONDITIONING", "link": 50}, {"label": "latent", "name": "latent", "shape": 7, "type": "LATENT", "link": 54}], "outputs": [{"label": "CONDITIONING", "name": "CONDITIONING", "type": "CONDITIONING", "links": [48]}], "properties": {"Node name for S&R": "ReferenceLatent", "cnr_id": "comfy-core", "ver": "0.3.44", "widget_ue_connectable": {}}, "widgets_values": []}, {"id": 51, "type": "TeaCache", "pos": [5629.427734375, 3130.39208984375], "size": [315, 154], "flags": {}, "order": 11, "mode": 0, "inputs": [{"label": "model", "name": "model", "type": "MODEL", "link": 58}], "outputs": [{"label": "model", "name": "model", "type": "MODEL", "links": [59]}], "properties": {"Node name for S&R": "TeaCache", "widget_ue_connectable": {}}, "widgets_values": ["flux-kontext", 0.22000000000000003, 0.20000000000000004, 0.9000000000000001, "cuda"]}, {"id": 24, "type": "K<PERSON><PERSON><PERSON>", "pos": [5834.7568359375, 3372.801513671875], "size": [270, 262], "flags": {}, "order": 23, "mode": 0, "inputs": [{"label": "model", "name": "model", "type": "MODEL", "link": 59}, {"label": "positive", "name": "positive", "type": "CONDITIONING", "link": 51}, {"label": "negative", "name": "negative", "type": "CONDITIONING", "link": 53}, {"label": "latent_image", "name": "latent_image", "type": "LATENT", "link": 32}], "outputs": [{"label": "LATENT", "name": "LATENT", "type": "LATENT", "links": [34]}], "properties": {"Node name for S&R": "K<PERSON><PERSON><PERSON>", "cnr_id": "comfy-core", "ver": "0.3.44", "widget_ue_connectable": {}}, "widgets_values": [874206368777206, "randomize", 20, 1, "uni_pc", "simple", 1]}, {"id": 19, "type": "UNETLoader", "pos": [4624.03369140625, 3120.39990234375], "size": [417.51580810546875, 82], "flags": {}, "order": 5, "mode": 0, "inputs": [], "outputs": [{"label": "MODEL", "name": "MODEL", "type": "MODEL", "links": [21]}], "properties": {"Node name for S&R": "UNETLoader", "cnr_id": "comfy-core", "ver": "0.3.44", "widget_ue_connectable": {}}, "widgets_values": ["flux1-dev-kontext_fp8_scaled.safetensors", "default"]}, {"id": 30, "type": "VAEEncode", "pos": [5396.73974609375, 3203.71630859375], "size": [140, 46], "flags": {}, "order": 20, "mode": 0, "inputs": [{"label": "pixels", "name": "pixels", "type": "IMAGE", "link": 31}, {"label": "vae", "name": "vae", "type": "VAE", "link": 45}], "outputs": [{"label": "LATENT", "name": "LATENT", "type": "LATENT", "links": [32, 54]}], "properties": {"Node name for S&R": "VAEEncode", "cnr_id": "comfy-core", "ver": "0.3.44", "widget_ue_connectable": {}}, "widgets_values": []}], "links": [[1, 2, 0, 1, 0, "TRANSNET_MODEL"], [5, 7, 0, 1, 1, "VIDEO"], [6, 1, 0, 8, 0, "LIST"], [7, 8, 0, 9, 2, "STRING"], [9, 1, 1, 11, 0, "STRING"], [11, 1, 0, 13, 0, "*"], [12, 13, 0, 12, 1, "INT"], [13, 14, 0, 12, 0, "*"], [14, 12, 1, 8, 1, "INT"], [16, 12, 2, 16, 0, "INT,FLOAT,IMAGE,LATENT"], [19, 9, 0, 18, 0, "IMAGE"], [20, 18, 0, 20, 0, "IMAGE"], [21, 19, 0, 22, 0, "MODEL"], [30, 18, 0, 29, 0, "IMAGE"], [31, 29, 0, 30, 0, "IMAGE"], [32, 30, 0, 24, 3, "LATENT"], [33, 35, 0, 22, 1, "CLIP"], [34, 24, 0, 36, 0, "LATENT"], [35, 37, 0, 36, 1, "VAE"], [36, 36, 0, 38, 0, "IMAGE"], [38, 12, 0, 40, 0, "FLOW_CONTROL"], [40, 39, 0, 40, 2, "*"], [41, 16, 0, 40, 1, "*"], [42, 36, 0, 39, 1, "*"], [43, 12, 3, 39, 0, "*"], [44, 40, 1, 41, 0, "IMAGE"], [45, 37, 0, 30, 1, "VAE"], [48, 44, 0, 45, 0, "CONDITIONING"], [49, 22, 1, 46, 0, "CLIP"], [50, 46, 0, 44, 0, "CONDITIONING"], [51, 45, 0, 24, 1, "CONDITIONING"], [52, 35, 0, 47, 0, "CLIP"], [53, 47, 0, 24, 2, "CONDITIONING"], [54, 30, 0, 44, 1, "LATENT"], [58, 22, 0, 51, 0, "MODEL"], [59, 51, 0, 24, 0, "MODEL"], [60, 40, 1, 52, 0, "IMAGE"]], "groups": [{"id": 1, "title": "Video Segment", "bounding": [2647.880859375, 3012.997314453125, 821.5013427734375, 560.0593872070312], "color": "#3f789e", "font_size": 24, "flags": {}}, {"id": 2, "title": "for loop start", "bounding": [3510.530517578125, 3015.61572265625, 1077.8818359375, 808.5914916992188], "color": "#3f789e", "font_size": 24, "flags": {}}, {"id": 3, "title": "Kontext Style Convert", "bounding": [4623.83447265625, 3021.560302734375, 1692.671875, 650.3158569335938], "color": "#3f789e", "font_size": 24, "flags": {}}, {"id": 4, "title": "for loop end", "bounding": [6396.78271484375, 3018.509033203125, 763.0494995117188, 666.9147338867188], "color": "#3f789e", "font_size": 24, "flags": {}}, {"id": 5, "title": "B站、Youtube：T8star-Aix", "bounding": [2758.7646484375, 2566.08056640625, 2973.7998046875, 385.0824279785156], "color": "#3f789e", "font_size": 240, "flags": {}}], "config": {}, "extra": {"ds": {"scale": 0.7972024500000015, "offset": [-2241.9035556843814, -2658.4639207894256]}, "links_added_by_ue": [], "VHS_KeepIntermediate": true, "ue_links": [], "VHS_MetadataImage": true, "0246.VERSION": [0, 0, 4], "VHS_latentpreviewrate": 0, "frontendVersion": "1.23.4", "VHS_latentpreview": false}, "version": 0.4}