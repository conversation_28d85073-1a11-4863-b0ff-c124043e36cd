{"id": "25dfce61-3571-4c9a-a7f8-6a83951d66aa", "revision": 0, "last_node_id": 134, "last_link_id": 185, "nodes": [{"id": 94, "type": "CLIPTextEncode", "pos": [8734.2548828125, 3741.30419921875], "size": [450, 150], "flags": {"collapsed": false}, "order": 9, "mode": 0, "inputs": [{"label": "clip", "name": "clip", "type": "CLIP", "link": 138}], "outputs": [{"label": "CONDITIONING", "name": "CONDITIONING", "type": "CONDITIONING", "links": [159, 163, 167]}], "properties": {"Node name for S&R": "CLIPTextEncode", "cnr_id": "comfy-core", "ver": "0.3.43", "widget_ue_connectable": {}}, "widgets_values": [""], "color": "#2a363b", "bgcolor": "#3f5159"}, {"id": 106, "type": "CLIPTextEncode", "pos": [8726.884765625, 3330.657470703125], "size": [450, 350], "flags": {}, "order": 25, "mode": 0, "inputs": [{"label": "clip", "name": "clip", "type": "CLIP", "link": 150}, {"label": "text", "name": "text", "type": "STRING", "widget": {"name": "text"}, "link": 151}], "outputs": [{"label": "CONDITIONING", "name": "CONDITIONING", "type": "CONDITIONING", "links": [158, 162, 166]}], "properties": {"Node name for S&R": "CLIPTextEncode", "cnr_id": "comfy-core", "ver": "0.3.43", "widget_ue_connectable": {}}, "widgets_values": [" 描绘了一位年轻女性脸部特写, 被透过薄纱窗帘的金色晨光照亮, 从侧面拍摄.\n在紧凑的1:1特写镜头中, 仅可见女性脸庞的一半, 略微转向附近的一扇窗户, 温暖, 清晨的阳光透过白色布帘倾泻而入.光线柔和地掠过她的面容, 突显出她脸颊上细腻的桃色绒毛和嘴唇附近干燥皮肤的微弱纹理.她的眼睛在画框边缘刚刚可见, 捕捉到一丝琥珀色光芒.漂浮在空气中的杂乱发丝在逆光中发光.背景模糊不清但显然是室内--可能是卧室--失焦并染上日黄色调.她表情中的轻微眯眼可见清晨的昏沉感.镜头在她脸部最亮的部分周围形成淡淡的光晕, 光线最强的区域有轻微的色彩溢出.感觉非常亲密, 像是醒来后拍下的第一张照片, 在寂静中捕捉."], "color": "#2a363b", "bgcolor": "#3f5159"}, {"id": 112, "type": "UNETLoader", "pos": [7923.2705078125, 4307.4189453125], "size": [430, 82], "flags": {}, "order": 0, "mode": 0, "inputs": [], "outputs": [{"label": "MODEL", "name": "MODEL", "type": "MODEL", "slot_index": 0, "links": [144]}], "properties": {"Node name for S&R": "UNETLoader", "widget_ue_connectable": {}}, "widgets_values": ["wan2.2_t2v_high_noise_14B_fp8_scaled.safetensors", "fp8_e4m3fn"], "color": "#223", "bgcolor": "#335"}, {"id": 109, "type": "CLIPLoader", "pos": [7952.6748046875, 3331.805908203125], "size": [391.4033508300781, 98], "flags": {}, "order": 1, "mode": 0, "inputs": [], "outputs": [{"label": "CLIP", "name": "CLIP", "type": "CLIP", "slot_index": 0, "links": [138, 150]}], "properties": {"Node name for S&R": "CLIPLoader", "cnr_id": "comfy-core", "ver": "0.3.44", "models": [{"name": "umt5_xxl_fp8_e4m3fn_scaled.safetensors", "url": "https://huggingface.co/Comfy-Org/Wan_2.1_ComfyUI_repackaged/resolve/main/split_files/text_encoders/umt5_xxl_fp8_e4m3fn_scaled.safetensors?download=true", "directory": "text_encoders"}], "widget_ue_connectable": {}}, "widgets_values": ["umt5_xxl_fp8_e4m3fn_scaled.safetensors", "wan", "default"], "color": "#432", "bgcolor": "#653"}, {"id": 108, "type": "VAELoader", "pos": [7961.41845703125, 3484.175537109375], "size": [367.7924499511719, 70.40070343017578], "flags": {}, "order": 2, "mode": 0, "inputs": [], "outputs": [{"label": "VAE", "name": "VAE", "type": "VAE", "slot_index": 0, "links": [155, 171]}], "properties": {"Node name for S&R": "VAELoader", "cnr_id": "comfy-core", "ver": "0.3.44", "models": [{"name": "wan_2.1_vae.safetensors", "url": "https://huggingface.co/Comfy-Org/Wan_2.1_ComfyUI_repackaged/resolve/main/split_files/vae/wan_2.1_vae.safetensors?download=true", "directory": "vae"}], "widget_ue_connectable": {}}, "widgets_values": ["wan_2.1_vae.safetensors"], "color": "#322", "bgcolor": "#533"}, {"id": 131, "type": "MathExpression|pysssss", "pos": [7959.72314453125, 3627.55078125], "size": [210, 128], "flags": {"collapsed": false}, "order": 12, "mode": 0, "inputs": [{"label": "a", "name": "a", "shape": 7, "type": "INT,FLOAT,IMAGE,LATENT", "link": 180}, {"label": "b", "name": "b", "shape": 7, "type": "INT,FLOAT,IMAGE,LATENT"}, {"label": "c", "name": "c", "shape": 7, "type": "INT,FLOAT,IMAGE,LATENT"}], "outputs": [{"label": "INT", "name": "INT", "type": "INT", "links": [181]}, {"label": "FLOAT", "name": "FLOAT", "type": "FLOAT"}], "properties": {"Node name for S&R": "MathExpression|pysssss", "widget_ue_connectable": {}}, "widgets_values": ["a/64"]}, {"id": 132, "type": "MathExpression|pysssss", "pos": [8190.9052734375, 3625.372314453125], "size": [210, 128], "flags": {"collapsed": false}, "order": 17, "mode": 0, "inputs": [{"label": "a", "name": "a", "shape": 7, "type": "INT,FLOAT,IMAGE,LATENT", "link": 181}, {"label": "b", "name": "b", "shape": 7, "type": "INT,FLOAT,IMAGE,LATENT"}, {"label": "c", "name": "c", "shape": 7, "type": "INT,FLOAT,IMAGE,LATENT"}], "outputs": [{"label": "INT", "name": "INT", "type": "INT", "links": [184]}, {"label": "FLOAT", "name": "FLOAT", "type": "FLOAT"}], "properties": {"Node name for S&R": "MathExpression|pysssss", "widget_ue_connectable": {}}, "widgets_values": ["a*64"]}, {"id": 133, "type": "MathExpression|pysssss", "pos": [7952.0498046875, 3890.947021484375], "size": [210, 128], "flags": {"collapsed": false}, "order": 13, "mode": 0, "inputs": [{"label": "a", "name": "a", "shape": 7, "type": "INT,FLOAT,IMAGE,LATENT", "link": 182}, {"label": "b", "name": "b", "shape": 7, "type": "INT,FLOAT,IMAGE,LATENT"}, {"label": "c", "name": "c", "shape": 7, "type": "INT,FLOAT,IMAGE,LATENT"}], "outputs": [{"label": "INT", "name": "INT", "type": "INT", "links": [183]}, {"label": "FLOAT", "name": "FLOAT", "type": "FLOAT"}], "properties": {"Node name for S&R": "MathExpression|pysssss", "widget_ue_connectable": {}}, "widgets_values": ["a/64"]}, {"id": 134, "type": "MathExpression|pysssss", "pos": [8191.11181640625, 3890.636474609375], "size": [210, 128], "flags": {"collapsed": false}, "order": 18, "mode": 0, "inputs": [{"label": "a", "name": "a", "shape": 7, "type": "INT,FLOAT,IMAGE,LATENT", "link": 183}, {"label": "b", "name": "b", "shape": 7, "type": "INT,FLOAT,IMAGE,LATENT"}, {"label": "c", "name": "c", "shape": 7, "type": "INT,FLOAT,IMAGE,LATENT"}], "outputs": [{"label": "INT", "name": "INT", "type": "INT", "links": [185]}, {"label": "FLOAT", "name": "FLOAT", "type": "FLOAT"}], "properties": {"Node name for S&R": "MathExpression|pysssss", "widget_ue_connectable": {}}, "widgets_values": ["a*64"]}, {"id": 126, "type": "ImpactSwitch", "pos": [7327.52392578125, 3578.63671875], "size": [246.57891845703125, 122], "flags": {}, "order": 16, "mode": 0, "inputs": [{"label": "input1", "name": "input1", "shape": 7, "type": "STRING", "link": 175}, {"label": "input2", "name": "input2", "type": "STRING", "link": 179}, {"label": "input3", "name": "input3", "type": "STRING"}], "outputs": [{"label": "STRING", "name": "STRING", "type": "STRING", "links": [153]}, {"label": "selected_label", "name": "selected_label", "type": "STRING"}, {"label": "selected_index", "name": "selected_index", "type": "INT"}], "properties": {"Node name for S&R": "ImpactSwitch", "widget_ue_connectable": {}}, "widgets_values": [2, false], "color": "#223", "bgcolor": "#335"}, {"id": 105, "type": "UNETLoader", "pos": [7924.197265625, 4449.94677734375], "size": [420.1534118652344, 83.15281677246094], "flags": {}, "order": 3, "mode": 0, "inputs": [], "outputs": [{"label": "MODEL", "name": "MODEL", "type": "MODEL", "slot_index": 0, "links": [145]}], "properties": {"Node name for S&R": "UNETLoader", "cnr_id": "comfy-core", "ver": "0.3.44", "models": [{"name": "wan2.1_t2v_1.3B_fp16.safetensors", "url": "https://huggingface.co/Comfy-Org/Wan_2.1_ComfyUI_repackaged/resolve/main/split_files/diffusion_models/wan2.1_t2v_1.3B_fp16.safetensors?download=true", "directory": "diffusion_models"}], "widget_ue_connectable": {}}, "widgets_values": ["wan2.2_t2v_low_noise_14B_fp8_scaled.safetensors", "fp8_e4m3fn"], "color": "#223", "bgcolor": "#335"}, {"id": 102, "type": "LoraLoaderModelOnly", "pos": [8924.4921875, 4298.72607421875], "size": [434.5848083496094, 82], "flags": {}, "order": 14, "mode": 4, "inputs": [{"label": "model", "name": "model", "type": "MODEL", "link": 147}], "outputs": [{"label": "MODEL", "name": "MODEL", "type": "MODEL", "links": [149]}], "properties": {"Node name for S&R": "LoraLoaderModelOnly", "widget_ue_connectable": {}}, "widgets_values": ["WAN2.1_SmartphoneSnapshotPhotoReality_v1_by-AI_Characters.safetensors", 1.0000000000000002], "color": "#223", "bgcolor": "#335"}, {"id": 103, "type": "LoraLoaderModelOnly", "pos": [8940.01171875, 4449.06689453125], "size": [434.5848083496094, 82], "flags": {}, "order": 15, "mode": 0, "inputs": [{"label": "model", "name": "model", "type": "MODEL", "link": 148}], "outputs": [{"label": "MODEL", "name": "MODEL", "type": "MODEL", "links": [141]}], "properties": {"Node name for S&R": "LoraLoaderModelOnly", "widget_ue_connectable": {}}, "widgets_values": ["WAN2.1_SmartphoneSnapshotPhotoReality_v1_by-AI_Characters.safetensors", 0.7000000000000002], "color": "#223", "bgcolor": "#335"}, {"id": 104, "type": "CFGZeroStarAndInit", "pos": [9406.8955078125, 4294.24560546875], "size": [315, 82], "flags": {}, "order": 19, "mode": 4, "inputs": [{"label": "model", "name": "model", "type": "MODEL", "link": 149}], "outputs": [{"label": "MODEL", "name": "MODEL", "type": "MODEL", "links": [142]}], "properties": {"Node name for S&R": "CFGZeroStarAndInit", "widget_ue_connectable": {}}, "widgets_values": [true, 0], "color": "#323", "bgcolor": "#535"}, {"id": 96, "type": "CFGZeroStarAndInit", "pos": [9413.794921875, 4448.72607421875], "size": [315, 82], "flags": {}, "order": 20, "mode": 0, "inputs": [{"label": "model", "name": "model", "type": "MODEL", "link": 141}], "outputs": [{"label": "MODEL", "name": "MODEL", "type": "MODEL", "links": [143]}], "properties": {"Node name for S&R": "CFGZeroStarAndInit", "widget_ue_connectable": {}}, "widgets_values": [true, 0], "color": "#323", "bgcolor": "#535"}, {"id": 97, "type": "PathchSageAttentionKJ", "pos": [9763.9560546875, 4298.74951171875], "size": [315, 58], "flags": {}, "order": 23, "mode": 0, "inputs": [{"label": "model", "name": "model", "type": "MODEL", "link": 142}], "outputs": [{"label": "MODEL", "name": "MODEL", "type": "MODEL", "links": [146]}], "properties": {"Node name for S&R": "PathchSageAttentionKJ", "widget_ue_connectable": {}}, "widgets_values": ["auto"], "color": "#223", "bgcolor": "#335"}, {"id": 98, "type": "PathchSageAttentionKJ", "pos": [9775.5185546875, 4448.02880859375], "size": [315, 58], "flags": {}, "order": 24, "mode": 0, "inputs": [{"label": "model", "name": "model", "type": "MODEL", "link": 143}], "outputs": [{"label": "MODEL", "name": "MODEL", "type": "MODEL", "links": [172]}], "properties": {"Node name for S&R": "PathchSageAttentionKJ", "widget_ue_connectable": {}}, "widgets_values": ["auto"], "color": "#223", "bgcolor": "#335"}, {"id": 101, "type": "ModelSamplingSD3", "pos": [10108.6484375, 4294.02880859375], "size": [210, 58], "flags": {}, "order": 27, "mode": 0, "inputs": [{"label": "model", "name": "model", "type": "MODEL", "link": 146}], "outputs": [{"label": "MODEL", "name": "MODEL", "type": "MODEL", "slot_index": 0, "links": [157]}], "properties": {"Node name for S&R": "ModelSamplingSD3", "cnr_id": "comfy-core", "ver": "0.3.45", "widget_ue_connectable": {}}, "widgets_values": [8], "color": "#223", "bgcolor": "#335"}, {"id": 118, "type": "ModelSamplingSD3", "pos": [10136.4912109375, 4444.8056640625], "size": [210, 58], "flags": {}, "order": 28, "mode": 0, "inputs": [{"label": "model", "name": "model", "type": "MODEL", "link": 172}], "outputs": [{"label": "MODEL", "name": "MODEL", "type": "MODEL", "slot_index": 0, "links": [161, 165]}], "properties": {"Node name for S&R": "ModelSamplingSD3", "cnr_id": "comfy-core", "ver": "0.3.45", "widget_ue_connectable": {}}, "widgets_values": [8.000000000000002], "color": "#223", "bgcolor": "#335"}, {"id": 125, "type": "Note", "pos": [7602.80908203125, 3602.753662109375], "size": [210, 88], "flags": {}, "order": 4, "mode": 0, "inputs": [], "outputs": [], "properties": {}, "widgets_values": ["🔝上面可以切换提示字类型，默认智能扩写\n1：自定义写提示词\n2：智能扩写提示词\n"], "color": "#432", "bgcolor": "#653"}, {"id": 95, "type": "EmptyHunyuanLatentVideo", "pos": [8737.701171875, 3941.30322265625], "size": [450, 150], "flags": {}, "order": 22, "mode": 0, "inputs": [{"label": "width", "name": "width", "type": "INT", "widget": {"name": "width"}, "link": 184}, {"label": "height", "name": "height", "type": "INT", "widget": {"name": "height"}, "link": 185}], "outputs": [{"label": "LATENT", "name": "LATENT", "type": "LATENT", "links": [160]}], "properties": {"Node name for S&R": "EmptyHunyuanLatentVideo", "cnr_id": "comfy-core", "ver": "0.3.43", "widget_ue_connectable": {}}, "widgets_values": [1536, 1536, 1, 1], "color": "#323", "bgcolor": "#535"}, {"id": 119, "type": "easy showAnything", "pos": [9953.83203125, 3337.160888671875], "size": [278.33135986328125, 248.69532775878906], "flags": {}, "order": 26, "mode": 0, "inputs": [{"label": "anything", "name": "anything", "shape": 7, "type": "*", "link": 173}], "outputs": [{"label": "output", "name": "output", "type": "*"}], "properties": {"Node name for S&R": "easy showAnything", "widget_ue_connectable": {}}, "widgets_values": ["2010年代初用手机拍摄并上传到Facebook的快照照片, 具有动态自然光照, 以及中性白色色彩平衡和褪色效果,A hyper-realistic portrait of an adult Asian woman sitting sideways at an aged elmwood table, her left hand gently supporting her chin while her right ring finger bears a worn silver ring. The soft, golden light of 4 PM filters through an intricately carved wooden window, casting a diamond-shaped glow on her cheekbone and creating faint shadows beneath her eyelashes. Her skin texture is meticulously detailed, with subtle freckles adorning her nose and a natural sheen highlighting the contours of her lips. Her hair, kissed by the sun with hints of light brown at the tips, delicately brushes against her collarbone as it moves with a gentle breeze. Her gaze holds an unspoken depth, as though capturing a fleeting moment of thoughtful stillness, suspended in time."], "color": "#232", "bgcolor": "#353"}, {"id": 107, "type": "CR Text Concatenate", "pos": [9628.0078125, 3355.601806640625], "size": [210, 126], "flags": {}, "order": 21, "mode": 0, "inputs": [{"label": "text1", "name": "text1", "shape": 7, "type": "STRING", "widget": {"name": "text1"}, "link": 152}, {"label": "text2", "name": "text2", "shape": 7, "type": "STRING", "widget": {"name": "text2"}, "link": 153}], "outputs": [{"label": "STRING", "name": "STRING", "type": "*", "links": [151, 173]}, {"label": "show_help", "name": "show_help", "type": "STRING"}], "properties": {"Node name for S&R": "CR Text Concatenate", "widget_ue_connectable": {}}, "widgets_values": ["", "", ""], "color": "#232", "bgcolor": "#353"}, {"id": 120, "type": "CR Text", "pos": [9293.6767578125, 3361.82763671875], "size": [268.0993957519531, 123.97663116455078], "flags": {}, "order": 5, "mode": 0, "inputs": [], "outputs": [{"label": "text", "name": "text", "type": "*", "links": [152]}, {"label": "show_help", "name": "show_help", "type": "STRING"}], "properties": {"Node name for S&R": "CR Text", "widget_ue_connectable": {}}, "widgets_values": ["2010年代初用手机拍摄并上传到Facebook的快照照片, 具有动态自然光照, 以及中性白色色彩平衡和褪色效果,"], "color": "#232", "bgcolor": "#353"}, {"id": 110, "type": "VAEDecode", "pos": [10195.572265625, 3751.2158203125], "size": [229.1857147216797, 48.448692321777344], "flags": {"collapsed": true}, "order": 33, "mode": 0, "inputs": [{"label": "samples", "name": "samples", "type": "LATENT", "link": 154}, {"label": "vae", "name": "vae", "type": "VAE", "link": 155}], "outputs": [{"label": "IMAGE", "name": "IMAGE", "type": "IMAGE", "links": [156]}], "properties": {"Node name for S&R": "VAEDecode", "cnr_id": "comfy-core", "ver": "0.3.43", "widget_ue_connectable": {}}, "widgets_values": [], "color": "#223", "bgcolor": "#335"}, {"id": 124, "type": "CR Text", "pos": [7324.54931640625, 3294.93994140625], "size": [484.3734436035156, 214.15890502929688], "flags": {}, "order": 6, "mode": 0, "inputs": [], "outputs": [{"label": "text", "name": "text", "type": "*", "links": [175, 177]}, {"label": "show_help", "name": "show_help", "type": "STRING"}], "properties": {"Node name for S&R": "CR Text", "widget_ue_connectable": {}}, "widgets_values": ["写实人像，成年亚洲女性侧坐于老榆木桌前，左手轻托下颌，右手无名指戴着一枚磨损的银戒。午后 4 点的自然光透过雕花木窗，在颧骨投下菱形光斑，睫毛在眼睑下方扫出浅淡阴影。皮肤肌理清晰可见 —— 鼻翼有细小雀斑，唇峰处泛着自然的唇纹光泽，发丝随微风拂过锁骨，发尾带着日晒后的浅棕色调。眼神里有未说尽的思索，像被时间温柔定格的瞬间。"], "color": "#223", "bgcolor": "#335"}, {"id": 116, "type": "SaveImage", "pos": [10416.576171875, 3287.90576171875], "size": [386.79571533203125, 555.2181396484375], "flags": {}, "order": 34, "mode": 0, "inputs": [{"label": "images", "name": "images", "type": "IMAGE", "link": 169}], "outputs": [], "properties": {"Node name for S&R": "SaveImage", "widget_ue_connectable": {}}, "widgets_values": ["ComfyUI", ""], "color": "#323", "bgcolor": "#535"}, {"id": 117, "type": "VAEDecode", "pos": [10002.3271484375, 3741.37255859375], "size": [171.85336303710938, 46], "flags": {"collapsed": true}, "order": 32, "mode": 0, "inputs": [{"label": "samples", "name": "samples", "type": "LATENT", "link": 170}, {"label": "vae", "name": "vae", "type": "VAE", "link": 171}], "outputs": [{"label": "IMAGE", "name": "IMAGE", "type": "IMAGE", "links": [169]}], "properties": {"Node name for S&R": "VAEDecode", "cnr_id": "comfy-core", "ver": "0.3.43", "widget_ue_connectable": {}}, "widgets_values": [], "color": "#323", "bgcolor": "#535"}, {"id": 114, "type": "KSamplerAdvanced", "pos": [9643.048828125, 3781.700439453125], "size": [304.748046875, 334], "flags": {}, "order": 30, "mode": 0, "inputs": [{"label": "model", "name": "model", "type": "MODEL", "link": 161}, {"label": "positive", "name": "positive", "type": "CONDITIONING", "link": 162}, {"label": "negative", "name": "negative", "type": "CONDITIONING", "link": 163}, {"label": "latent_image", "name": "latent_image", "type": "LATENT", "link": 164}], "outputs": [{"label": "LATENT", "name": "LATENT", "type": "LATENT", "links": [170]}], "properties": {"Node name for S&R": "KSamplerAdvanced", "widget_ue_connectable": {}}, "widgets_values": ["disable", 467906809928932, "randomize", 10, 1, "euler", "simple", 5, 10000, "disable"], "color": "#323", "bgcolor": "#535"}, {"id": 113, "type": "KSamplerAdvanced", "pos": [9309.796875, 3785.360107421875], "size": [304.748046875, 334], "flags": {}, "order": 29, "mode": 0, "inputs": [{"label": "model", "name": "model", "type": "MODEL", "link": 157}, {"label": "positive", "name": "positive", "type": "CONDITIONING", "link": 158}, {"label": "negative", "name": "negative", "type": "CONDITIONING", "link": 159}, {"label": "latent_image", "name": "latent_image", "type": "LATENT", "link": 160}], "outputs": [{"label": "LATENT", "name": "LATENT", "type": "LATENT", "links": [164, 168]}], "properties": {"Node name for S&R": "KSamplerAdvanced", "widget_ue_connectable": {}}, "widgets_values": ["enable", 196425706483580, "randomize", 10, 1, "euler", "simple", 0, 5, "enable"], "color": "#323", "bgcolor": "#535"}, {"id": 130, "type": "FluxResolutionNode", "pos": [7330.783203125, 3781.1884765625], "size": [474.677734375, 224.8619384765625], "flags": {}, "order": 7, "mode": 0, "inputs": [], "outputs": [{"label": "width", "name": "width", "shape": 3, "type": "INT", "slot_index": 0, "links": [180]}, {"label": "height", "name": "height", "shape": 3, "type": "INT", "slot_index": 1, "links": [182]}, {"label": "resolution", "name": "resolution", "shape": 3, "type": "STRING"}], "properties": {"Node name for S&R": "FluxResolutionNode", "widget_ue_connectable": {}}, "widgets_values": ["2.0", "3:4 (<PERSON>)", false, "1:1"], "color": "#232", "bgcolor": "#353"}, {"id": 115, "type": "KSamplerAdvanced", "pos": [10000.189453125, 3790.302001953125], "size": [304.748046875, 334], "flags": {}, "order": 31, "mode": 0, "inputs": [{"label": "model", "name": "model", "type": "MODEL", "link": 165}, {"label": "positive", "name": "positive", "type": "CONDITIONING", "link": 166}, {"label": "negative", "name": "negative", "type": "CONDITIONING", "link": 167}, {"label": "latent_image", "name": "latent_image", "type": "LATENT", "link": 168}], "outputs": [{"label": "LATENT", "name": "LATENT", "type": "LATENT", "links": [154]}], "properties": {"Node name for S&R": "KSamplerAdvanced", "widget_ue_connectable": {}}, "widgets_values": ["disable", 860817279649181, "randomize", 10, 1, "euler", "beta", 5, 10000, "disable"], "color": "#223", "bgcolor": "#335"}, {"id": 100, "type": "LoraLoaderModelOnly", "pos": [8380.369140625, 4458.72607421875], "size": [516.1165161132812, 82], "flags": {}, "order": 10, "mode": 0, "inputs": [{"label": "model", "name": "model", "type": "MODEL", "link": 145}], "outputs": [{"label": "MODEL", "name": "MODEL", "type": "MODEL", "links": [148]}], "properties": {"Node name for S&R": "LoraLoaderModelOnly", "widget_ue_connectable": {}}, "widgets_values": ["Wan21_T2V_14B_lightx2v_cfg_step_distill_lora_rank32.safetensors", 1.0000000000000002], "color": "#223", "bgcolor": "#335"}, {"id": 99, "type": "LoraLoaderModelOnly", "pos": [8390.369140625, 4308.72607421875], "size": [494.0005798339844, 83.3822250366211], "flags": {}, "order": 8, "mode": 0, "inputs": [{"label": "model", "name": "model", "type": "MODEL", "link": 144}], "outputs": [{"label": "MODEL", "name": "MODEL", "type": "MODEL", "links": [147]}], "properties": {"Node name for S&R": "LoraLoaderModelOnly", "widget_ue_connectable": {}}, "widgets_values": ["Wan21_T2V_14B_lightx2v_cfg_step_distill_lora_rank32.safetensors", 1.0000000000000002], "color": "#223", "bgcolor": "#335"}, {"id": 111, "type": "SaveImage", "pos": [10415.8232421875, 3962.902099609375], "size": [417.4447021484375, 584.60986328125], "flags": {}, "order": 35, "mode": 0, "inputs": [{"label": "images", "name": "images", "type": "IMAGE", "link": 156}], "outputs": [], "properties": {"Node name for S&R": "SaveImage", "widget_ue_connectable": {}}, "widgets_values": ["ComfyUI", ""], "color": "#223", "bgcolor": "#335"}, {"id": 128, "type": "RH_LLMAPI_NODE", "pos": [8422.052734375, 3342.2802734375], "size": [285.88201904296875, 528.957763671875], "flags": {}, "order": 11, "mode": 0, "inputs": [{"label": "ref_image", "name": "ref_image", "shape": 7, "type": "IMAGE"}, {"label": "prompt", "name": "prompt", "type": "STRING", "widget": {"name": "prompt"}, "link": 177}], "outputs": [{"label": "describe", "name": "describe", "type": "STRING", "links": [179]}], "properties": {"Node name for S&R": "RH_LLMAPI_NODE", "widget_ue_connectable": {}}, "widgets_values": ["", "", "", "# FLUX Prompt 反推提示词说明\n\n## 🧠 角色定位\n你是一位富有艺术感的 **FLUX Prompt 助理**，具备对图像的深度理解能力，能够将视觉内容转化为丰富、生动、具体的文本提示词（prompt），以用于图像生成模型 FLUX 或 Stable Diffusion。\n\n---\n\n## 🎯 核心任务\n\n我将提供一张图片或一个简短的主题描述，你的任务是：\n\n1. **理解图像/主题**：分析图像中的内容、元素、情感与风格。\n2. **生成 Prompt**：根据分析结果，输出一段详尽的英文 prompt，用于高质量图像生成。\n\n---\n\n## 🖼️ 图像分析维度\n\n请从以下角度描述图像内容，确保 prompt 丰富、准确、具象：\n\n- **主要元素**：人物、动物、物体、风景等核心对象\n- **画面细节**：颜色、纹理、光影、服饰、姿态、动作、表情、环境构成等（不少于5处具体细节）\n- **场景氛围**：温馨、神秘、奇幻、宁静、末世感等\n- **艺术风格**：现实主义、赛博朋克、油画风、水彩、卡通、像素风、未来主义等\n- **构图视角**：如“俯视”、“仰视”、“特写”、“广角”等\n\n---\n\n## ✏️ Prompt 输出格式要求\n\n- **语言**：仅使用中文生成 prompt\n- **语气**：描述性强、画面感明确，避免口语化或模糊措辞\n- **结构**：连贯自然，不分条目，形成一段完整描述\n- **长度**：足够详尽，建议不少于60词\n- **内容限制**：\n  - 不解释 prompt 内容\n  - 不添加“生成提示词”、“Prompt:”等前缀\n\n---\n\n## ✅ 示例\n\n- **输入主题**：一只飞在雪山上的龙\n- **输出 prompt**：\n\n  > 一条雄伟的绿鳞巨龙，眼中泛着琥珀色光芒，双翼张开，飞翔在令人叹为观止的雪山群中。它强壮的身影投下长长的阴影，笼罩着高耸入云的山峰。下方是一条清澈的河流，在深谷中蜿蜒流淌，倒映着明亮的天空。空气中弥漫着飘渺的薄雾，营造出清新而梦幻的氛围。这幅画面展现了令人敬畏的自然与野性之美。", "", 0.6, 1603, "randomize"], "color": "#232", "bgcolor": "#353"}], "links": [[138, 109, 0, 94, 0, "CLIP"], [141, 103, 0, 96, 0, "MODEL"], [142, 104, 0, 97, 0, "MODEL"], [143, 96, 0, 98, 0, "MODEL"], [144, 112, 0, 99, 0, "MODEL"], [145, 105, 0, 100, 0, "MODEL"], [146, 97, 0, 101, 0, "MODEL"], [147, 99, 0, 102, 0, "MODEL"], [148, 100, 0, 103, 0, "MODEL"], [149, 102, 0, 104, 0, "MODEL"], [150, 109, 0, 106, 0, "CLIP"], [151, 107, 0, 106, 1, "STRING"], [152, 120, 0, 107, 0, "STRING"], [153, 126, 0, 107, 1, "STRING"], [154, 115, 0, 110, 0, "LATENT"], [155, 108, 0, 110, 1, "VAE"], [156, 110, 0, 111, 0, "IMAGE"], [157, 101, 0, 113, 0, "MODEL"], [158, 106, 0, 113, 1, "CONDITIONING"], [159, 94, 0, 113, 2, "CONDITIONING"], [160, 95, 0, 113, 3, "LATENT"], [161, 118, 0, 114, 0, "MODEL"], [162, 106, 0, 114, 1, "CONDITIONING"], [163, 94, 0, 114, 2, "CONDITIONING"], [164, 113, 0, 114, 3, "LATENT"], [165, 118, 0, 115, 0, "MODEL"], [166, 106, 0, 115, 1, "CONDITIONING"], [167, 94, 0, 115, 2, "CONDITIONING"], [168, 113, 0, 115, 3, "LATENT"], [169, 117, 0, 116, 0, "IMAGE"], [170, 114, 0, 117, 0, "LATENT"], [171, 108, 0, 117, 1, "VAE"], [172, 98, 0, 118, 0, "MODEL"], [173, 107, 0, 119, 0, "*"], [175, 124, 0, 126, 0, "STRING"], [177, 124, 0, 128, 1, "STRING"], [179, 128, 0, 126, 1, "STRING"], [180, 130, 0, 131, 0, "INT,FLOAT,IMAGE,LATENT"], [181, 131, 0, 132, 0, "INT,FLOAT,IMAGE,LATENT"], [182, 130, 1, 133, 0, "INT,FLOAT,IMAGE,LATENT"], [183, 133, 0, 134, 0, "INT,FLOAT,IMAGE,LATENT"], [184, 132, 0, 95, 0, "INT"], [185, 134, 0, 95, 1, "INT"]], "groups": [{"id": 10, "title": "写实限定", "bounding": [9251.8876953125, 3253.50146484375, 1111.16357421875, 391.36846923828125], "color": "#3f789e", "font_size": 24, "flags": {}}, {"id": 11, "title": "Group", "bounding": [7914.25439453125, 3251.30517578125, 1285.0025634765625, 914.3989868164062], "color": "#3f789e", "font_size": 24, "flags": {}}, {"id": 12, "title": "模型加载", "bounding": [7910.37255859375, 4208.72607421875, 2475.119140625, 358.1201477050781], "color": "#8A8", "font_size": 24, "flags": {}}, {"id": 13, "title": "采样", "bounding": [9248.43359375, 3669.2197265625, 1121.0775146484375, 496.71514892578125], "color": "#3f789e", "font_size": 24, "flags": {}}], "config": {}, "extra": {"ds": {"scale": 0.4090909090909133, "offset": [-6316.307275217106, -2786.5893142252767]}, "VHS_KeepIntermediate": true, "links_added_by_ue": [], "VHS_MetadataImage": true, "ue_links": [], "0246.VERSION": [0, 0, 4], "VHS_latentpreviewrate": 0, "frontendVersion": "1.23.4", "VHS_latentpreview": false}, "version": 0.4}