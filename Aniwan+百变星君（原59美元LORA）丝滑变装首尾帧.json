{"id": "c6e410bc-5e2c-460b-ae81-c91b6094fbb1", "revision": 0, "last_node_id": 330, "last_link_id": 535, "nodes": [{"id": 285, "type": "GetNode", "pos": [3344.19677734375, 400.4292297363281], "size": [210, 58], "flags": {"collapsed": true}, "order": 0, "mode": 0, "inputs": [], "outputs": [{"label": "WANVAE", "name": "WANVAE", "type": "WANVAE", "links": [514]}], "title": "Get_WanVAE", "properties": {"widget_ue_connectable": {}}, "widgets_values": ["WanVAE"], "color": "#322", "bgcolor": "#533"}, {"id": 286, "type": "GetNode", "pos": [3574.19677734375, 370.4292297363281], "size": [210, 58], "flags": {"collapsed": true}, "order": 1, "mode": 0, "inputs": [], "outputs": [{"label": "WANVIDEOMODEL", "name": "WANVIDEOMODEL", "type": "WANVIDEOMODEL", "links": [530]}], "title": "Get_WanModel", "properties": {"widget_ue_connectable": {}}, "widgets_values": ["WanModel"], "color": "#223", "bgcolor": "#335"}, {"id": 287, "type": "GetNode", "pos": [4124.19677734375, 230.42922973632812], "size": [210, 58], "flags": {"collapsed": true}, "order": 2, "mode": 0, "inputs": [], "outputs": [{"label": "IMAGE", "name": "IMAGE", "type": "IMAGE", "links": [491]}], "title": "Get_start_image", "properties": {"widget_ue_connectable": {}}, "widgets_values": ["start_image"], "color": "#2a363b", "bgcolor": "#3f5159"}, {"id": 288, "type": "GetNode", "pos": [4124.19677734375, 280.4292297363281], "size": [210, 58], "flags": {"collapsed": true}, "order": 3, "mode": 0, "inputs": [], "outputs": [{"label": "IMAGE", "name": "IMAGE", "type": "IMAGE", "links": [492]}], "title": "Get_end_image", "properties": {"widget_ue_connectable": {}}, "widgets_values": ["end_image"], "color": "#2a363b", "bgcolor": "#3f5159"}, {"id": 289, "type": "EmptyImage", "pos": [4584.19677734375, 280.4292297363281], "size": [315, 130], "flags": {"collapsed": true}, "order": 41, "mode": 0, "inputs": [{"label": "height", "name": "height", "type": "INT", "widget": {"name": "height"}, "link": 490}], "outputs": [{"label": "IMAGE", "name": "IMAGE", "type": "IMAGE", "links": [494]}], "properties": {"Node name for S&R": "EmptyImage", "cnr_id": "comfy-core", "ver": "0.3.26", "widget_ue_connectable": {"height": true}, "ttNbgOverride": {"bgcolor": "#593930", "groupcolor": "#b06634", "color": "#332922"}}, "widgets_values": [8, 512, 1, 0], "color": "#332922", "bgcolor": "#593930"}, {"id": 290, "type": "AddLabel", "pos": [4314.19677734375, 230.42922973632812], "size": [315, 294], "flags": {"collapsed": true}, "order": 19, "mode": 0, "inputs": [{"label": "image", "name": "image", "type": "IMAGE", "link": 491}, {"label": "caption", "name": "caption", "shape": 7, "type": "STRING"}], "outputs": [{"label": "IMAGE", "name": "IMAGE", "type": "IMAGE", "links": [497]}], "properties": {"Node name for S&R": "AddLabel", "cnr_id": "comfyui-kjnodes", "ver": "52c2e31a903fec2dd654fb614ea82ba2757d5028", "widget_ue_connectable": {}, "ttNbgOverride": {"bgcolor": "#593930", "groupcolor": "#b06634", "color": "#332922"}}, "widgets_values": [10, 2, 48, 32, "white", "black", "FreeMono.ttf", "start_frame", "up", ""], "color": "#332922", "bgcolor": "#593930"}, {"id": 291, "type": "AddLabel", "pos": [4324.19677734375, 280.4292297363281], "size": [315, 294], "flags": {"collapsed": true}, "order": 20, "mode": 0, "inputs": [{"label": "image", "name": "image", "type": "IMAGE", "link": 492}, {"label": "caption", "name": "caption", "shape": 7, "type": "STRING"}], "outputs": [{"label": "IMAGE", "name": "IMAGE", "type": "IMAGE", "links": [498]}], "properties": {"Node name for S&R": "AddLabel", "cnr_id": "comfyui-kjnodes", "ver": "52c2e31a903fec2dd654fb614ea82ba2757d5028", "widget_ue_connectable": {}, "ttNbgOverride": {"bgcolor": "#593930", "groupcolor": "#b06634", "color": "#332922"}}, "widgets_values": [10, 2, 48, 32, "white", "black", "FreeMono.ttf", "end_frame", "up", ""], "color": "#332922", "bgcolor": "#593930"}, {"id": 292, "type": "ImageConcatMulti", "pos": [4574.19677734375, 320.4292297363281], "size": [315, 170], "flags": {}, "order": 42, "mode": 0, "inputs": [{"label": "image_1", "name": "image_1", "type": "IMAGE", "link": 493}, {"label": "image_2", "name": "image_2", "shape": 7, "type": "IMAGE", "link": 494}, {"label": "image_3", "name": "image_3", "type": "IMAGE", "link": 495}], "outputs": [{"label": "images", "name": "images", "type": "IMAGE", "links": [528]}], "properties": {"cnr_id": "comfyui-kjnodes", "ver": "52c2e31a903fec2dd654fb614ea82ba2757d5028", "widget_ue_connectable": {}, "ttNbgOverride": {"bgcolor": "#593930", "groupcolor": "#b06634", "color": "#332922"}}, "widgets_values": [3, "left", true, null], "color": "#332922", "bgcolor": "#593930"}, {"id": 293, "type": "GetImageSizeAndCount", "pos": [4154.19677734375, 810.4288330078125], "size": [277.20001220703125, 86], "flags": {}, "order": 39, "mode": 0, "inputs": [{"label": "image", "name": "image", "type": "IMAGE", "link": 496}], "outputs": [{"label": "image", "name": "image", "type": "IMAGE", "links": [493]}, {"label": "width", "name": "width", "type": "INT"}, {"label": "height", "name": "height", "type": "INT", "links": [490]}, {"label": "count", "name": "count", "type": "INT"}], "properties": {"Node name for S&R": "GetImageSizeAndCount", "cnr_id": "comfyui-kjnodes", "ver": "52c2e31a903fec2dd654fb614ea82ba2757d5028", "widget_ue_connectable": {}, "ttNbgOverride": {"bgcolor": "#593930", "groupcolor": "#b06634", "color": "#332922"}}, "widgets_values": [], "color": "#332922", "bgcolor": "#593930"}, {"id": 294, "type": "ImageConcatMulti", "pos": [4144.19677734375, 330.4292297363281], "size": [315, 150], "flags": {}, "order": 26, "mode": 0, "inputs": [{"label": "image_1", "name": "image_1", "type": "IMAGE", "link": 497}, {"label": "image_2", "name": "image_2", "shape": 7, "type": "IMAGE", "link": 498}], "outputs": [{"label": "images", "name": "images", "type": "IMAGE", "links": [495]}], "properties": {"cnr_id": "comfyui-kjnodes", "ver": "52c2e31a903fec2dd654fb614ea82ba2757d5028", "widget_ue_connectable": {}, "ttNbgOverride": {"bgcolor": "#593930", "groupcolor": "#b06634", "color": "#332922"}}, "widgets_values": [2, "down", true, null], "color": "#332922", "bgcolor": "#593930"}, {"id": 295, "type": "SetNode", "pos": [1952.0343017578125, 805.4591064453125], "size": [210, 58], "flags": {"collapsed": true}, "order": 30, "mode": 0, "inputs": [{"label": "IMAGE", "name": "IMAGE", "type": "IMAGE", "link": 499}], "outputs": [{"label": "IMAGE", "name": "IMAGE", "type": "IMAGE", "links": [521]}], "title": "Set_IMAGE", "properties": {"previousName": "", "widget_ue_connectable": {}}, "widgets_values": ["start_image"], "color": "#2a363b", "bgcolor": "#3f5159"}, {"id": 296, "type": "SetNode", "pos": [1956.9892578125, 859.9617309570312], "size": [210, 58], "flags": {"collapsed": true}, "order": 27, "mode": 0, "inputs": [{"label": "IMAGE", "name": "IMAGE", "type": "IMAGE", "link": 500}], "outputs": [{"label": "IMAGE", "name": "IMAGE", "type": "IMAGE", "links": [522]}], "title": "Set_IMAGE", "properties": {"previousName": "", "widget_ue_connectable": {}}, "widgets_values": ["end_image"], "color": "#2a363b", "bgcolor": "#3f5159"}, {"id": 297, "type": "GetNode", "pos": [3914.197021484375, 300.4292297363281], "size": [210, 58], "flags": {"collapsed": true}, "order": 4, "mode": 0, "inputs": [], "outputs": [{"label": "WANVAE", "name": "WANVAE", "type": "WANVAE", "links": [504]}], "title": "Get_WanVAE", "properties": {"widget_ue_connectable": {}}, "widgets_values": ["WanVAE"], "color": "#322", "bgcolor": "#533"}, {"id": 298, "type": "SetNode", "pos": [3313.78125, -706.451171875], "size": [210, 58], "flags": {"collapsed": false}, "order": 31, "mode": 0, "inputs": [{"label": "WANVIDEOMODEL", "name": "WANVIDEOMODEL", "type": "WANVIDEOMODEL", "link": 501}], "outputs": [{"label": "*", "name": "*", "type": "*"}], "title": "Set_WANVIDEOMODEL", "properties": {"previousName": "", "widget_ue_connectable": {}}, "widgets_values": ["WanModel"], "color": "#223", "bgcolor": "#335"}, {"id": 299, "type": "SetNode", "pos": [2756.952392578125, -218.79244995117188], "size": [210, 58], "flags": {"collapsed": true}, "order": 22, "mode": 0, "inputs": [{"label": "WANTEXTENCODER", "name": "WANTEXTENCODER", "type": "WANTEXTENCODER", "link": 502}], "outputs": [{"label": "*", "name": "*", "type": "*"}], "title": "Set_WANTEXTENCODER", "properties": {"previousName": "", "widget_ue_connectable": {}}, "widgets_values": ["WanTextEncoder"], "color": "#432", "bgcolor": "#653"}, {"id": 300, "type": "SetNode", "pos": [2757.834228515625, -330.5542907714844], "size": [210, 58], "flags": {"collapsed": true}, "order": 21, "mode": 0, "inputs": [{"label": "WANVAE", "name": "WANVAE", "type": "WANVAE", "link": 503}], "outputs": [{"label": "*", "name": "*", "type": "*"}], "title": "Set_WANVAE", "properties": {"previousName": "", "widget_ue_connectable": {}}, "widgets_values": ["WanVAE"], "color": "#322", "bgcolor": "#533"}, {"id": 301, "type": "WanVideoSLG", "pos": [3336.4521484375, 872.3551635742188], "size": [315, 106], "flags": {}, "order": 5, "mode": 0, "inputs": [], "outputs": [{"label": "slg_args", "name": "slg_args", "type": "SLGARGS", "links": [534]}], "properties": {"Node name for S&R": "WanVideoSLG", "cnr_id": "ComfyUI-WanVideoWrapper", "ver": "2d2a184723dae88388e130659f51c36fcadeaba8", "widget_ue_connectable": {}, "ttNbgOverride": {"bgcolor": "#593930", "groupcolor": "#b06634", "color": "#332922"}}, "widgets_values": ["9,10", 0.30000000000000004, 0.7000000000000002], "color": "#332922", "bgcolor": "#593930"}, {"id": 302, "type": "WanVideoExperimentalArgs", "pos": [3336.8974609375, 1094.4608154296875], "size": [327.5999755859375, 250], "flags": {}, "order": 6, "mode": 4, "inputs": [], "outputs": [{"label": "exp_args", "name": "exp_args", "type": "EXPERIMENTALARGS", "links": [535]}], "properties": {"Node name for S&R": "WanVideoExperimentalArgs", "cnr_id": "ComfyUI-WanVideoWrapper", "ver": "2d2a184723dae88388e130659f51c36fcadeaba8", "widget_ue_connectable": {}, "ttNbgOverride": {"bgcolor": "#593930", "groupcolor": "#b06634", "color": "#332922"}}, "widgets_values": ["", true, false, 0, false, 1, 1.25, 20, false], "color": "#332922", "bgcolor": "#593930"}, {"id": 303, "type": "WanVideoVAELoader", "pos": [2353.989990234375, -387.5807800292969], "size": [315, 82], "flags": {}, "order": 7, "mode": 0, "inputs": [], "outputs": [{"label": "vae", "name": "vae", "type": "WANVAE", "slot_index": 0, "links": [503]}], "properties": {"Node name for S&R": "WanVideoVAELoader", "cnr_id": "ComfyUI-WanVideoWrapper", "ver": "3869b0482b615b6a8fd6f346467c5ef6627eed72", "widget_ue_connectable": {}}, "widgets_values": ["Wan2_1_VAE_bf16.safetensors", "bf16"], "color": "#322", "bgcolor": "#533"}, {"id": 304, "type": "LoadWanVideoT5TextEncoder", "pos": [2343.43359375, -239.55783081054688], "size": [377.1661376953125, 130], "flags": {}, "order": 8, "mode": 0, "inputs": [], "outputs": [{"label": "wan_t5_model", "name": "wan_t5_model", "type": "WANTEXTENCODER", "slot_index": 0, "links": [502]}], "properties": {"Node name for S&R": "LoadWanVideoT5TextEncoder", "cnr_id": "ComfyUI-WanVideoWrapper", "ver": "3869b0482b615b6a8fd6f346467c5ef6627eed72", "widget_ue_connectable": {}}, "widgets_values": ["umt5-xxl-enc-bf16.safetensors", "bf16", "offload_device", "disabled"], "color": "#432", "bgcolor": "#653"}, {"id": 305, "type": "GetNode", "pos": [2460.43505859375, 259.4717102050781], "size": [210, 58], "flags": {"collapsed": true}, "order": 9, "mode": 0, "inputs": [], "outputs": [{"label": "WANTEXTENCODER", "name": "WANTEXTENCODER", "type": "WANTEXTENCODER", "links": [526]}], "title": "Get_WanTextEncoder", "properties": {"widget_ue_connectable": {}}, "widgets_values": ["WanTextEncoder"], "color": "#432", "bgcolor": "#653"}, {"id": 306, "type": "GetNode", "pos": [2467.73583984375, 337.2284240722656], "size": [210, 58], "flags": {"collapsed": true}, "order": 10, "mode": 0, "inputs": [], "outputs": [{"label": "WANVIDEOMODEL", "name": "WANVIDEOMODEL", "type": "WANVIDEOMODEL", "links": [527]}], "title": "Get_WanModel", "properties": {"widget_ue_connectable": {}}, "widgets_values": ["WanModel"], "color": "#223", "bgcolor": "#335"}, {"id": 307, "type": "WanVideoDecode", "pos": [4154.19677734375, 540.4291381835938], "size": [315, 198], "flags": {}, "order": 38, "mode": 0, "inputs": [{"label": "vae", "name": "vae", "type": "WANVAE", "link": 504}, {"label": "samples", "name": "samples", "type": "LATENT", "link": 505}], "outputs": [{"label": "images", "name": "images", "type": "IMAGE", "links": [496, 506]}], "properties": {"Node name for S&R": "WanVideoDecode", "cnr_id": "ComfyUI-WanVideoWrapper", "ver": "d9b1f4d1a5aea91d101ae97a54714a5861af3f50", "widget_ue_connectable": {}}, "widgets_values": [false, 272, 272, 144, 128, "default"], "color": "#322", "bgcolor": "#533"}, {"id": 311, "type": "Note", "pos": [1748.0262451171875, -826.************], "size": [366.************, 88], "flags": {}, "order": 11, "mode": 0, "inputs": [], "outputs": [], "properties": {}, "widgets_values": ["lora模型地址\n\nhttps://www.liblib.art/modelinfo/3bb8af11864a44c0bf40339ff09d3ad4?from=personal_page&versionUuid=e2015fa07522401e887a8d4560c4b336"], "color": "#432", "bgcolor": "#653"}, {"id": 312, "type": "Note", "pos": [2159.************, 1564.5074462890625], "size": [268.0067443847656, 94.53243255615234], "flags": {}, "order": 12, "mode": 0, "inputs": [], "outputs": [], "properties": {}, "widgets_values": ["可以在这里改分辨率"], "color": "#432", "bgcolor": "#653"}, {"id": 313, "type": "WanVideoTeaCache", "pos": [3353.************, 111.21353149414062], "size": [315, 178], "flags": {}, "order": 13, "mode": 0, "inputs": [], "outputs": [{"label": "cache_args", "name": "cache_args", "type": "CACHEARGS", "links": [533]}], "properties": {"Node name for S&R": "WanVideoTeaCache", "cnr_id": "ComfyUI-WanVideoWrapper", "ver": "3869b0482b615b6a8fd6f346467c5ef6627eed72", "widget_ue_connectable": {}, "ttNbgOverride": {"bgcolor": "#593930", "groupcolor": "#b06634", "color": "#332922"}}, "widgets_values": [0.15000000000000002, 1, -1, "offload_device", "true", "e"], "color": "#332922", "bgcolor": "#593930"}, {"id": 314, "type": "ImageResizeKJ", "pos": [2132.1201171875, 1255.304443359375], "size": [315, 266], "flags": {}, "order": 24, "mode": 0, "inputs": [{"label": "image", "name": "image", "type": "IMAGE", "link": 507}, {"label": "get_image_size", "name": "get_image_size", "shape": 7, "type": "IMAGE"}], "outputs": [{"label": "IMAGE", "name": "IMAGE", "type": "IMAGE", "links": [500]}, {"label": "width", "name": "width", "type": "INT", "links": [524]}, {"label": "height", "name": "height", "type": "INT", "links": [525]}], "properties": {"Node name for S&R": "ImageResizeKJ", "cnr_id": "comfyui-kjnodes", "ver": "0addfc6101f7a834c7fb6e0a1b26529360ab5350", "widget_ue_connectable": {}, "ttNbgOverride": {"bgcolor": "#593930", "groupcolor": "#b06634", "color": "#332922"}}, "widgets_values": [544, 960, "lanc<PERSON>s", true, 2, 0], "color": "#332922", "bgcolor": "#593930"}, {"id": 315, "type": "MaskPreview+", "pos": [2525.7158203125, 1319.8521728515625], "size": [711.4246215820312, 262.9547424316406], "flags": {}, "order": 34, "mode": 0, "inputs": [{"label": "mask", "name": "mask", "type": "MASK", "link": 508}], "outputs": [], "properties": {"Node name for S&R": "MaskPreview+", "cnr_id": "comfyui_essentials", "aux_id": "kijai/ComfyUI_essentials", "ver": "76e9d1e4399bd025ce8b12c290753d58f9f53e93", "widget_ue_connectable": {}, "ttNbgOverride": {"bgcolor": "#593930", "groupcolor": "#b06634", "color": "#332922"}}, "widgets_values": [], "color": "#332922", "bgcolor": "#593930"}, {"id": 316, "type": "GetImageSizeAndCount", "pos": [2598.78759765625, 705.3148803710938], "size": [277.20001220703125, 86], "flags": {}, "order": 33, "mode": 0, "inputs": [{"label": "image", "name": "image", "type": "IMAGE", "link": 509}], "outputs": [{"label": "image", "name": "image", "type": "IMAGE", "links": [510, 515]}, {"label": "width", "name": "width", "type": "INT", "links": [518]}, {"label": "height", "name": "height", "type": "INT", "links": [519]}, {"label": "count", "name": "count", "type": "INT", "links": [520]}], "properties": {"Node name for S&R": "GetImageSizeAndCount", "cnr_id": "comfyui-kjnodes", "ver": "2aa4da0f587f68c1c255b0152ed8f5c334ebe4b4", "widget_ue_connectable": {}, "ttNbgOverride": {"bgcolor": "#593930", "groupcolor": "#b06634", "color": "#332922"}}, "widgets_values": [], "color": "#332922", "bgcolor": "#593930"}, {"id": 317, "type": "LoadImage", "pos": [1612.130859375, 662.9424438476562], "size": [292.45867919921875, 396.9063415527344], "flags": {}, "order": 14, "mode": 0, "inputs": [], "outputs": [{"label": "IMAGE", "name": "IMAGE", "type": "IMAGE", "links": [523]}, {"label": "MASK", "name": "MASK", "type": "MASK"}], "properties": {"Node name for S&R": "LoadImage", "cnr_id": "comfy-core", "ver": "0.3.27", "widget_ue_connectable": {}, "ttNbgOverride": {"bgcolor": "#593930", "groupcolor": "#b06634", "color": "#332922"}}, "widgets_values": ["2cc192457e757806a264963b06ad225eb574b422da66974875ff11882072c16d.png", "image", ""], "color": "#332922", "bgcolor": "#593930"}, {"id": 318, "type": "LoadImage", "pos": [1614.890625, 1162.2371826171875], "size": [292.45867919921875, 396.9063415527344], "flags": {}, "order": 15, "mode": 0, "inputs": [], "outputs": [{"label": "IMAGE", "name": "IMAGE", "type": "IMAGE", "links": [507]}, {"label": "MASK", "name": "MASK", "type": "MASK"}], "properties": {"Node name for S&R": "LoadImage", "cnr_id": "comfy-core", "ver": "0.3.27", "widget_ue_connectable": {}, "ttNbgOverride": {"bgcolor": "#593930", "groupcolor": "#b06634", "color": "#332922"}}, "widgets_values": ["7927e4d857857546bb30962d284aba6c19acbeba9fd8cb34fc25680288d39c34.png", "image", ""], "color": "#332922", "bgcolor": "#593930"}, {"id": 319, "type": "PreviewImage", "pos": [2514.428466796875, 994.3333740234375], "size": [725.2979125976562, 258], "flags": {}, "order": 35, "mode": 0, "inputs": [{"label": "images", "name": "images", "type": "IMAGE", "link": 510}], "outputs": [], "properties": {"Node name for S&R": "PreviewImage", "cnr_id": "comfy-core", "ver": "0.3.27", "widget_ue_connectable": {}, "ttNbgOverride": {"bgcolor": "#593930", "groupcolor": "#b06634", "color": "#332922"}}, "widgets_values": [], "color": "#332922", "bgcolor": "#593930"}, {"id": 320, "type": "WanVideoBlockSwap", "pos": [2371.912109375, -782.5234375], "size": [315, 154], "flags": {}, "order": 16, "mode": 0, "inputs": [], "outputs": [{"label": "block_swap_args", "name": "block_swap_args", "type": "BLOCKSWAPARGS", "slot_index": 0, "links": [511]}], "properties": {"Node name for S&R": "WanVideoBlockSwap", "cnr_id": "ComfyUI-WanVideoWrapper", "ver": "3869b0482b615b6a8fd6f346467c5ef6627eed72", "widget_ue_connectable": {}}, "widgets_values": [40, false, false, true, 15], "color": "#223", "bgcolor": "#335"}, {"id": 321, "type": "WanVideoVACEModelSelect", "pos": [2289.914306640625, -535.3717041015625], "size": [398.6070251464844, 58], "flags": {}, "order": 17, "mode": 0, "inputs": [], "outputs": [{"label": "vace_model", "name": "vace_model", "type": "VACEPATH", "links": [513]}], "properties": {"Node name for S&R": "WanVideoVACEModelSelect", "cnr_id": "ComfyUI-WanVideoWrapper", "ver": "f2bc29b931983e279d25452d284b3888c8c81346", "widget_ue_connectable": {}, "ttNbgOverride": {"bgcolor": "#593930", "groupcolor": "#b06634", "color": "#332922"}}, "widgets_values": ["Wan2_1-VACE_module_14B_bf16.safetensors"], "color": "#332922", "bgcolor": "#593930"}, {"id": 322, "type": "WanVideoModelLoader", "pos": [2803.79736328125, -712.361083984375], "size": [477.4410095214844, 274], "flags": {}, "order": 29, "mode": 0, "inputs": [{"label": "compile_args", "name": "compile_args", "shape": 7, "type": "WANCOMPILEARGS"}, {"label": "block_swap_args", "name": "block_swap_args", "shape": 7, "type": "BLOCKSWAPARGS", "link": 511}, {"label": "lora", "name": "lora", "shape": 7, "type": "WANVIDLORA", "link": 512}, {"label": "vram_management_args", "name": "vram_management_args", "shape": 7, "type": "VRAM_MANAGEMENTARGS"}, {"label": "vace_model", "name": "vace_model", "shape": 7, "type": "VACEPATH", "link": 513}, {"label": "fantasytalking_model", "name": "fantasytalking_model", "shape": 7, "type": "FANTASYTALKINGMODEL"}, {"label": "multitalk_model", "name": "multitalk_model", "shape": 7, "type": "MULTITALKMODEL"}], "outputs": [{"label": "model", "name": "model", "type": "WANVIDEOMODEL", "slot_index": 0, "links": [501]}], "properties": {"Node name for S&R": "WanVideoModelLoader", "cnr_id": "ComfyUI-WanVideoWrapper", "ver": "3869b0482b615b6a8fd6f346467c5ef6627eed72", "widget_ue_connectable": {}}, "widgets_values": ["aniWan2114BFp8E4m3fn_t2v.safetensors", "bf16", "fp8_e4m3fn", "offload_device", "sageattn"], "color": "#223", "bgcolor": "#335"}, {"id": 323, "type": "WanVideoVACEEncode", "pos": [3260.728759765625, 502.2890319824219], "size": [343.59088134765625, 334], "flags": {}, "order": 36, "mode": 0, "inputs": [{"label": "vae", "name": "vae", "type": "WANVAE", "link": 514}, {"label": "input_frames", "name": "input_frames", "shape": 7, "type": "IMAGE", "link": 515}, {"label": "ref_images", "name": "ref_images", "shape": 7, "type": "IMAGE", "link": 516}, {"label": "input_masks", "name": "input_masks", "shape": 7, "type": "MASK", "link": 517}, {"label": "prev_vace_embeds", "name": "prev_vace_embeds", "shape": 7, "type": "WANVIDIMAGE_EMBEDS"}, {"label": "width", "name": "width", "type": "INT", "widget": {"name": "width"}, "link": 518}, {"label": "height", "name": "height", "type": "INT", "widget": {"name": "height"}, "link": 519}, {"label": "num_frames", "name": "num_frames", "type": "INT", "widget": {"name": "num_frames"}, "link": 520}], "outputs": [{"label": "vace_embeds", "name": "vace_embeds", "type": "WANVIDIMAGE_EMBEDS", "links": [532]}], "properties": {"Node name for S&R": "WanVideoVACEEncode", "cnr_id": "ComfyUI-WanVideoWrapper", "ver": "3869b0482b615b6a8fd6f346467c5ef6627eed72", "widget_ue_connectable": {"width": true, "num_frames": true, "height": true}}, "widgets_values": [480, 832, 29, 1.0000000000000002, 0, 1, false], "color": "#322", "bgcolor": "#533"}, {"id": 324, "type": "WanVideoVACEStartToEndFrame", "pos": [2096.431884765625, 633.8816528320312], "size": [403.1999816894531, 190], "flags": {"collapsed": false}, "order": 32, "mode": 0, "inputs": [{"label": "start_image", "name": "start_image", "shape": 7, "type": "IMAGE", "link": 521}, {"label": "end_image", "name": "end_image", "shape": 7, "type": "IMAGE", "link": 522}, {"label": "control_images", "name": "control_images", "shape": 7, "type": "IMAGE"}, {"label": "inpaint_mask", "name": "inpaint_mask", "shape": 7, "type": "MASK"}], "outputs": [{"label": "images", "name": "images", "type": "IMAGE", "links": [509]}, {"label": "masks", "name": "masks", "type": "MASK", "links": [508, 517]}], "properties": {"Node name for S&R": "WanVideoVACEStartToEndFrame", "cnr_id": "ComfyUI-WanVideoWrapper", "ver": "ce8d908ed64df328e5bcb692eddc4fa53ad82c49", "widget_ue_connectable": {}, "ttNbgOverride": {"bgcolor": "#593930", "groupcolor": "#b06634", "color": "#332922"}}, "widgets_values": [81, 0, 0, -1], "color": "#332922", "bgcolor": "#593930"}, {"id": 325, "type": "ImageResizeKJ", "pos": [2133.079345703125, 917.871826171875], "size": [315, 266], "flags": {}, "order": 28, "mode": 0, "inputs": [{"label": "image", "name": "image", "type": "IMAGE", "link": 523}, {"label": "get_image_size", "name": "get_image_size", "shape": 7, "type": "IMAGE"}, {"label": "width", "name": "width", "type": "INT", "widget": {"name": "width"}, "link": 524}, {"label": "height", "name": "height", "type": "INT", "widget": {"name": "height"}, "link": 525}], "outputs": [{"label": "IMAGE", "name": "IMAGE", "type": "IMAGE", "links": [499, 516]}, {"label": "width", "name": "width", "type": "INT"}, {"label": "height", "name": "height", "type": "INT"}], "properties": {"Node name for S&R": "ImageResizeKJ", "cnr_id": "comfyui-kjnodes", "ver": "0addfc6101f7a834c7fb6e0a1b26529360ab5350", "widget_ue_connectable": {}, "ttNbgOverride": {"bgcolor": "#593930", "groupcolor": "#b06634", "color": "#332922"}}, "widgets_values": [480, 720, "lanc<PERSON>s", true, 2, 0], "color": "#332922", "bgcolor": "#593930"}, {"id": 326, "type": "WanVideoTextEncode", "pos": [1041.51513671875, 860.9800415039062], "size": [502.2525939941406, 303.338623046875], "flags": {}, "order": 23, "mode": 0, "inputs": [{"label": "t5", "name": "t5", "shape": 7, "type": "WANTEXTENCODER", "link": 526}, {"label": "model_to_offload", "name": "model_to_offload", "shape": 7, "type": "WANVIDEOMODEL", "link": 527}], "outputs": [{"label": "text_embeds", "name": "text_embeds", "type": "WANVIDEOTEXTEMBEDS", "slot_index": 0, "links": [531]}], "properties": {"Node name for S&R": "WanVideoTextEncode", "cnr_id": "ComfyUI-WanVideoWrapper", "ver": "3869b0482b615b6a8fd6f346467c5ef6627eed72", "widget_ue_connectable": {}}, "widgets_values": ["gfbz,在一位穿着白色短裙粉色上亿的女人从室内走到了户外，行走的同时服装变成粉色旗袍，", "colorful, bad quality, blurry, messy, chaotic", true, false, "gpu"], "color": "#432", "bgcolor": "#653"}, {"id": 327, "type": "VHS_VideoCombine", "pos": [4994.43505859375, 1058.7537841796875], "size": [605.903076171875, 358], "flags": {}, "order": 43, "mode": 0, "inputs": [{"label": "images", "name": "images", "type": "IMAGE", "link": 528}, {"label": "audio", "name": "audio", "shape": 7, "type": "AUDIO"}, {"label": "meta_batch", "name": "meta_batch", "shape": 7, "type": "VHS_BatchManager"}, {"label": "vae", "name": "vae", "shape": 7, "type": "VAE"}], "outputs": [{"label": "Filenames", "name": "Filenames", "type": "VHS_FILENAMES"}], "properties": {"Node name for S&R": "VHS_VideoCombine", "cnr_id": "comfyui-videohelpersuite", "ver": "0a75c7958fe320efcb052f1d9f8451fd20c730a8", "widget_ue_connectable": {}, "ttNbgOverride": {"bgcolor": "#593930", "groupcolor": "#b06634", "color": "#332922"}}, "widgets_values": {"frame_rate": 16, "loop_count": 0, "filename_prefix": "WanVideoWrapper_VACE_startendframe", "format": "video/h264-mp4", "pix_fmt": "yuv420p", "crf": 19, "save_metadata": false, "trim_to_audio": false, "pingpong": false, "save_output": true, "no_preview": false, "videopreview": {"paused": false, "hidden": false, "params": {"filename": "WanVideoWrapper_VACE_startendframe_00002_sokda_1754034557.mp4", "workflow": "WanVideoWrapper_VACE_startendframe_00002.png", "fullpath": "/data/ComfyUI/personal/0c5e29f80acd84973c41e3a9c4cab22f/output/WanVideoWrapper_VACE_startendframe_00002.mp4", "format": "video/h264-mp4", "subfolder": "", "type": "output", "frame_rate": 16}}}, "color": "#332922", "bgcolor": "#593930"}, {"id": 328, "type": "WanVideoLoraSelect", "pos": [1931.49560546875, -625.7867431640625], "size": [315, 150], "flags": {}, "order": 25, "mode": 0, "inputs": [{"label": "prev_lora", "name": "prev_lora", "shape": 7, "type": "WANVIDLORA", "link": 529}, {"label": "blocks", "name": "blocks", "shape": 7, "type": "SELECTEDBLOCKS"}], "outputs": [{"label": "lora", "name": "lora", "type": "WANVIDLORA", "links": [512]}], "properties": {"Node name for S&R": "WanVideoLoraSelect", "widget_ue_connectable": {}, "ttNbgOverride": {"bgcolor": "#593930", "groupcolor": "#b06634", "color": "#332922"}}, "widgets_values": ["百变H-gfbz-050.safetensors", 1, false, true], "color": "#332922", "bgcolor": "#593930"}, {"id": 330, "type": "WanVideoSampler", "pos": [3743.480712890625, 367.5345764160156], "size": [312.8956298828125, 1124.5374755859375], "flags": {}, "order": 37, "mode": 0, "inputs": [{"label": "model", "name": "model", "type": "WANVIDEOMODEL", "link": 530}, {"label": "text_embeds", "name": "text_embeds", "type": "WANVIDEOTEXTEMBEDS", "link": 531}, {"label": "image_embeds", "name": "image_embeds", "type": "WANVIDIMAGE_EMBEDS", "link": 532}, {"label": "samples", "name": "samples", "shape": 7, "type": "LATENT"}, {"label": "feta_args", "name": "feta_args", "shape": 7, "type": "FETAARGS"}, {"label": "context_options", "name": "context_options", "shape": 7, "type": "WANVIDCONTEXT"}, {"label": "cache_args", "name": "cache_args", "shape": 7, "type": "CACHEARGS", "link": 533}, {"label": "flowedit_args", "name": "flowedit_args", "shape": 7, "type": "FLOWEDITARGS"}, {"label": "slg_args", "name": "slg_args", "shape": 7, "type": "SLGARGS", "link": 534}, {"label": "loop_args", "name": "loop_args", "shape": 7, "type": "LOOPARGS"}, {"label": "experimental_args", "name": "experimental_args", "shape": 7, "type": "EXPERIMENTALARGS", "link": 535}, {"label": "sigmas", "name": "sigmas", "shape": 7, "type": "SIGMAS"}, {"label": "unianimate_poses", "name": "unianimate_poses", "shape": 7, "type": "UNIANIMATE_POSE"}, {"label": "fantasytalking_embeds", "name": "fantasytalking_embeds", "shape": 7, "type": "FANTASYTALKING_EMBEDS"}, {"label": "uni3c_embeds", "name": "uni3c_embeds", "shape": 7, "type": "UNI3C_EMBEDS"}, {"label": "multitalk_embeds", "name": "multitalk_embeds", "shape": 7, "type": "MULTITALK_EMBEDS"}, {"label": "freeinit_args", "name": "freeinit_args", "shape": 7, "type": "FREEINITARGS"}], "outputs": [{"label": "samples", "name": "samples", "type": "LATENT", "links": [505]}], "properties": {"Node name for S&R": "WanVideoSampler", "cnr_id": "ComfyUI-WanVideoWrapper", "ver": "2d2a184723dae88388e130659f51c36fcadeaba8", "widget_ue_connectable": {}, "ttNbgOverride": {"bgcolor": "#593930", "groupcolor": "#b06634", "color": "#332922"}}, "widgets_values": [6, 1.0000000000000002, 8.000000000000002, 440335433356531, "randomize", true, "unipc", 0, 1, false, "comfy", 0, -1], "color": "#332922", "bgcolor": "#593930"}, {"id": 329, "type": "WanVideoLoraSelect", "pos": [1608.1593017578125, -416.3885803222656], "size": [315, 150], "flags": {}, "order": 18, "mode": 0, "inputs": [{"label": "prev_lora", "name": "prev_lora", "shape": 7, "type": "WANVIDLORA"}, {"label": "blocks", "name": "blocks", "shape": 7, "type": "SELECTEDBLOCKS"}], "outputs": [{"label": "lora", "name": "lora", "type": "WANVIDLORA", "links": [529]}], "properties": {"Node name for S&R": "WanVideoLoraSelect", "widget_ue_connectable": {}}, "widgets_values": ["Wan21_I2V_14B_lightx2v_cfg_step_distill_lora_rank64.safetensors", 1, false, true]}, {"id": 308, "type": "VHS_VideoCombine", "pos": [4243.25146484375, 956.041259765625], "size": [605.903076171875, 358], "flags": {}, "order": 40, "mode": 0, "inputs": [{"label": "images", "name": "images", "type": "IMAGE", "link": 506}, {"label": "audio", "name": "audio", "shape": 7, "type": "AUDIO"}, {"label": "meta_batch", "name": "meta_batch", "shape": 7, "type": "VHS_BatchManager"}, {"label": "vae", "name": "vae", "shape": 7, "type": "VAE"}], "outputs": [{"label": "Filenames", "name": "Filenames", "type": "VHS_FILENAMES"}], "properties": {"Node name for S&R": "VHS_VideoCombine", "cnr_id": "comfyui-videohelpersuite", "ver": "0a75c7958fe320efcb052f1d9f8451fd20c730a8", "widget_ue_connectable": {}, "ttNbgOverride": {"bgcolor": "#593930", "groupcolor": "#b06634", "color": "#332922"}}, "widgets_values": {"frame_rate": 16, "loop_count": 0, "filename_prefix": "WanVideoWrapper_VACE_startendframe", "format": "video/h264-mp4", "pix_fmt": "yuv420p", "crf": 19, "save_metadata": false, "trim_to_audio": false, "pingpong": false, "save_output": true, "no_preview": false, "videopreview": {"paused": false, "hidden": false, "params": {"filename": "WanVideoWrapper_VACE_startendframe_00001_hqjkl_1754034538.mp4", "workflow": "WanVideoWrapper_VACE_startendframe_00001.png", "fullpath": "/data/ComfyUI/personal/0c5e29f80acd84973c41e3a9c4cab22f/output/WanVideoWrapper_VACE_startendframe_00001.mp4", "format": "video/h264-mp4", "subfolder": "", "type": "output", "frame_rate": 16}}}, "color": "#332922", "bgcolor": "#593930"}], "links": [[490, 293, 2, 289, 0, "INT"], [491, 287, 0, 290, 0, "IMAGE"], [492, 288, 0, 291, 0, "IMAGE"], [493, 293, 0, 292, 0, "IMAGE"], [494, 289, 0, 292, 1, "IMAGE"], [495, 294, 0, 292, 2, "IMAGE"], [496, 307, 0, 293, 0, "IMAGE"], [497, 290, 0, 294, 0, "IMAGE"], [498, 291, 0, 294, 1, "IMAGE"], [499, 325, 0, 295, 0, "*"], [500, 314, 0, 296, 0, "*"], [501, 322, 0, 298, 0, "*"], [502, 304, 0, 299, 0, "*"], [503, 303, 0, 300, 0, "*"], [504, 297, 0, 307, 0, "WANVAE"], [505, 330, 0, 307, 1, "LATENT"], [506, 307, 0, 308, 0, "IMAGE"], [507, 318, 0, 314, 0, "IMAGE"], [508, 324, 1, 315, 0, "MASK"], [509, 324, 0, 316, 0, "IMAGE"], [510, 316, 0, 319, 0, "IMAGE"], [511, 320, 0, 322, 1, "BLOCKSWAPARGS"], [512, 328, 0, 322, 2, "WANVIDLORA"], [513, 321, 0, 322, 4, "VACEPATH"], [514, 285, 0, 323, 0, "WANVAE"], [515, 316, 0, 323, 1, "IMAGE"], [516, 325, 0, 323, 2, "IMAGE"], [517, 324, 1, 323, 3, "MASK"], [518, 316, 1, 323, 5, "INT"], [519, 316, 2, 323, 6, "INT"], [520, 316, 3, 323, 7, "INT"], [521, 295, 0, 324, 0, "IMAGE"], [522, 296, 0, 324, 1, "IMAGE"], [523, 317, 0, 325, 0, "IMAGE"], [524, 314, 1, 325, 2, "INT"], [525, 314, 2, 325, 3, "INT"], [526, 305, 0, 326, 0, "WANTEXTENCODER"], [527, 306, 0, 326, 1, "WANVIDEOMODEL"], [528, 292, 0, 327, 0, "IMAGE"], [529, 329, 0, 328, 0, "WANVIDLORA"], [530, 286, 0, 330, 0, "WANVIDEOMODEL"], [531, 326, 0, 330, 1, "WANVIDEOTEXTEMBEDS"], [532, 323, 0, 330, 2, "WANVIDIMAGE_EMBEDS"], [533, 313, 0, 330, 6, "CACHEARGS"], [534, 301, 0, 330, 8, "SLGARGS"], [535, 302, 0, 330, 10, "EXPERIMENTALARGS"]], "groups": [{"id": 8, "title": "模型加载", "bounding": [1575.553955078125, -900.65087890625, 2027.610107421875, 807.8640747070312], "color": "#88A", "font_size": 24, "flags": {}}, {"id": 9, "title": "首尾帧", "bounding": [1579.494140625, -35.49362564086914, 4272.4208984375, 2566.67138671875], "color": "#3f789e", "font_size": 24, "flags": {}}, {"id": 10, "title": "百变lora-vace首尾帧工作流-H", "bounding": [3689.757568359375, -877.740966796875, 1990.0491943359375, 213.27633666992188], "color": "#3f789e", "font_size": 150, "flags": {"pinned": true}}], "config": {}, "extra": {"ds": {"scale": 0.37190082644628125, "offset": [461.1904286879378, 200.86575242553732]}, "VHS_KeepIntermediate": true, "links_added_by_ue": [], "VHS_MetadataImage": true, "ue_links": [], "0246.VERSION": [0, 0, 4], "VHS_latentpreviewrate": 0, "frontendVersion": "1.20.7", "VHS_latentpreview": false, "node_versions": {"ComfyUI-WanVideoWrapper": "5a2383621a05825d0d0437781afcb8552d9590fd", "ComfyUI-VideoHelperSuite": "0a75c7958fe320efcb052f1d9f8451fd20c730a8", "comfy-core": "0.3.26"}}, "version": 0.4}