{"id": "43764d5f-5097-400c-9317-7128333411fb", "revision": 0, "last_node_id": 83, "last_link_id": 94, "nodes": [{"id": 18, "type": "JWInteger", "pos": [2875.28662109375, 979.2005004882812], "size": [315, 58], "flags": {}, "order": 0, "mode": 0, "inputs": [], "outputs": [{"label": "INT", "name": "INT", "type": "INT", "links": [23]}], "title": "多少秒", "properties": {"Node name for S&R": "JWInteger", "ttNbgOverride": {"bgcolor": "#335", "groupcolor": "#88A", "color": "#223"}}, "widgets_values": [5], "color": "#223", "bgcolor": "#335"}, {"id": 26, "type": "Note", "pos": [3347.455810546875, 1555.423828125], "size": [222.7895050048828, 88], "flags": {}, "order": 1, "mode": 0, "inputs": [], "outputs": [], "properties": {}, "widgets_values": ["1 手动填写提示词\n2 自动反推提示词"], "color": "#432", "bgcolor": "#653"}, {"id": 29, "type": "LoadImage", "pos": [3302.44140625, 915.6858520507812], "size": [292.45867919921875, 396.9063415527344], "flags": {}, "order": 2, "mode": 0, "inputs": [], "outputs": [{"label": "IMAGE", "name": "IMAGE", "type": "IMAGE", "links": [18]}, {"label": "MASK", "name": "MASK", "type": "MASK"}], "properties": {"Node name for S&R": "LoadImage", "cnr_id": "comfy-core", "ver": "0.3.27", "widget_ue_connectable": {}}, "widgets_values": ["8f666572df07eeb8c4bdef9cd121842e9390e5f56970d58396e7cc703837a73f.webp", "image", ""], "color": "#232", "bgcolor": "#353"}, {"id": 25, "type": "JWInteger", "pos": [2882.66552734375, 1160.9539794921875], "size": [315, 58], "flags": {}, "order": 3, "mode": 0, "inputs": [], "outputs": [{"label": "INT", "name": "INT", "type": "INT", "links": [19]}], "properties": {"Node name for S&R": "JWInteger", "ttNbgOverride": {"bgcolor": "#335", "groupcolor": "#88A", "color": "#223"}}, "widgets_values": [832], "color": "#223", "bgcolor": "#335"}, {"id": 59, "type": "PathchSageAttentionKJ", "pos": [1990, 1220], "size": [315, 58], "flags": {}, "order": 22, "mode": 0, "inputs": [{"label": "model", "name": "model", "type": "MODEL", "link": 63}], "outputs": [{"label": "MODEL", "name": "MODEL", "type": "MODEL", "links": [66]}], "properties": {"Node name for S&R": "PathchSageAttentionKJ", "widget_ue_connectable": {}}, "widgets_values": ["auto"], "color": "#223", "bgcolor": "#335"}, {"id": 63, "type": "ModelSamplingSD3", "pos": [1980, 1360], "size": [315, 58], "flags": {}, "order": 25, "mode": 0, "inputs": [{"label": "model", "name": "model", "type": "MODEL", "link": 66}], "outputs": [{"label": "MODEL", "name": "MODEL", "type": "MODEL", "slot_index": 0, "links": [65]}], "properties": {"Node name for S&R": "ModelSamplingSD3", "widget_ue_connectable": {}}, "widgets_values": [8.000000000000002], "color": "#223", "bgcolor": "#335"}, {"id": 66, "type": "CLIPLoader", "pos": [2070, 1510], "size": [316.59063720703125, 98], "flags": {}, "order": 4, "mode": 0, "inputs": [], "outputs": [{"label": "CLIP", "name": "CLIP", "type": "CLIP", "slot_index": 0, "links": [68]}], "properties": {"Node name for S&R": "CLIPLoader", "widget_ue_connectable": {}}, "widgets_values": ["umt5_xxl_fp8_e4m3fn_scaled.safetensors", "wan", "default"], "color": "#223", "bgcolor": "#335"}, {"id": 68, "type": "SetNode", "pos": [1828.4493408203125, 1536.155517578125], "size": [210, 58], "flags": {}, "order": 17, "mode": 0, "inputs": [{"label": "VAE", "name": "VAE", "type": "VAE", "link": 69}], "outputs": [{"label": "*", "name": "*", "type": "*"}], "title": "Set_VAE", "properties": {"previousName": ""}, "widgets_values": ["VAE"], "color": "#322", "bgcolor": "#533"}, {"id": 65, "type": "VAELoader", "pos": [1443.4669189453125, 1536.120361328125], "size": [360, 58], "flags": {}, "order": 5, "mode": 0, "inputs": [], "outputs": [{"label": "VAE", "name": "VAE", "type": "VAE", "slot_index": 0, "links": [69]}], "properties": {"Node name for S&R": "VAELoader", "widget_ue_connectable": {}}, "widgets_values": ["wan_2.1_vae.safetensors"], "color": "#223", "bgcolor": "#335"}, {"id": 70, "type": "GetNode", "pos": [3073.3330078125, 1916.66650390625], "size": [210, 58], "flags": {}, "order": 6, "mode": 0, "inputs": [], "outputs": [{"label": "MODEL", "name": "MODEL", "type": "MODEL", "links": [71]}], "properties": {}, "widgets_values": ["model1"], "color": "#223", "bgcolor": "#335"}, {"id": 69, "type": "GetNode", "pos": [3433.3330078125, 1916.66650390625], "size": [210, 58], "flags": {}, "order": 7, "mode": 0, "inputs": [], "outputs": [{"label": "MODEL", "name": "MODEL", "type": "MODEL", "links": [72]}], "title": "Get_model2", "properties": {}, "widgets_values": ["model2"], "color": "#223", "bgcolor": "#335"}, {"id": 77, "type": "CLIPTextEncode", "pos": [2234.605224609375, 2084.078369140625], "size": [425.27801513671875, 180.6060791015625], "flags": {}, "order": 18, "mode": 0, "inputs": [{"label": "clip", "name": "clip", "type": "CLIP", "link": 90}], "outputs": [{"label": "CONDITIONING", "name": "CONDITIONING", "type": "CONDITIONING", "slot_index": 0, "links": [78]}], "properties": {"Node name for S&R": "CLIPTextEncode", "widget_ue_connectable": {}}, "widgets_values": ["色调艳丽，过曝，静态，细节模糊不清，字幕，风格，作品，画作，画面，静止，整体发灰，最差质量，低质量，JPEG压缩残留，丑陋的，残缺的，多余的手指，画得不好的手部，画得不好的脸部，畸形的，毁容的，形态畸形的肢体，手指融合，静止不动的画面，杂乱的背景，三条腿，背景人很多，倒着走"], "color": "#322", "bgcolor": "#533"}, {"id": 76, "type": "CLIPTextEncode", "pos": [2219.380615234375, 1846.693603515625], "size": [422.84503173828125, 164.31304931640625], "flags": {}, "order": 28, "mode": 0, "inputs": [{"label": "clip", "name": "clip", "type": "CLIP", "link": 85}, {"label": "text", "name": "text", "type": "STRING", "widget": {"name": "text"}, "link": 83}], "outputs": [{"label": "CONDITIONING", "name": "CONDITIONING", "type": "CONDITIONING", "slot_index": 0, "links": [77]}], "properties": {"Node name for S&R": "CLIPTextEncode", "widget_ue_connectable": {}}, "widgets_values": ["A girl crouched down and picked up a coin from the ground"], "color": "#232", "bgcolor": "#353"}, {"id": 78, "type": "GetNode", "pos": [1886.2415771484375, 2427.703369140625], "size": [210, 58], "flags": {}, "order": 8, "mode": 0, "inputs": [], "outputs": [{"label": "VAE", "name": "VAE", "type": "VAE", "links": [79, 86]}], "properties": {}, "widgets_values": ["VAE"], "color": "#322", "bgcolor": "#533"}, {"id": 79, "type": "GetNode", "pos": [1884.107421875, 2307.588134765625], "size": [210, 58], "flags": {}, "order": 9, "mode": 0, "inputs": [], "outputs": [{"label": "CLIP", "name": "CLIP", "type": "CLIP", "links": [85, 90]}], "properties": {}, "widgets_values": ["CLIP"], "color": "#432", "bgcolor": "#653"}, {"id": 24, "type": "MathExpression|pysssss", "pos": [1815.4793701171875, 2129.580322265625], "size": [286.8543395996094, 128], "flags": {}, "order": 14, "mode": 0, "inputs": [{"label": "a", "name": "a", "shape": 7, "type": "INT,FLOAT,IMAGE,LATENT", "link": 23}, {"label": "b", "name": "b", "shape": 7, "type": "INT,FLOAT,IMAGE,LATENT"}, {"label": "c", "name": "c", "shape": 7, "type": "INT,FLOAT,IMAGE,LATENT"}], "outputs": [{"label": "INT", "name": "INT", "type": "INT", "links": [82]}, {"label": "FLOAT", "name": "FLOAT", "type": "FLOAT"}], "properties": {"Node name for S&R": "MathExpression|pysssss"}, "widgets_values": ["a*16+1"], "color": "#223", "bgcolor": "#335"}, {"id": 57, "type": "ModelSamplingSD3", "pos": [1980, 1070], "size": [315, 58], "flags": {}, "order": 26, "mode": 0, "inputs": [{"label": "model", "name": "model", "type": "MODEL", "link": 62}], "outputs": [{"label": "MODEL", "name": "MODEL", "type": "MODEL", "slot_index": 0, "links": [60]}], "properties": {"Node name for S&R": "ModelSamplingSD3", "widget_ue_connectable": {}}, "widgets_values": [8.000000000000002], "color": "#223", "bgcolor": "#335"}, {"id": 58, "type": "SetNode", "pos": [2370, 1070], "size": [210, 58], "flags": {}, "order": 30, "mode": 0, "inputs": [{"label": "MODEL", "name": "MODEL", "type": "MODEL", "link": 60}], "outputs": [{"label": "*", "name": "*", "type": "*"}], "title": "Set_MODEL", "properties": {"previousName": ""}, "widgets_values": ["model1"], "color": "#223", "bgcolor": "#335"}, {"id": 56, "type": "PathchSageAttentionKJ", "pos": [1980, 940], "size": [315, 58], "flags": {}, "order": 23, "mode": 0, "inputs": [{"label": "model", "name": "model", "type": "MODEL", "link": 59}], "outputs": [{"label": "MODEL", "name": "MODEL", "type": "MODEL", "links": [62]}], "properties": {"Node name for S&R": "PathchSageAttentionKJ", "widget_ue_connectable": {}}, "widgets_values": ["auto"], "color": "#223", "bgcolor": "#335"}, {"id": 62, "type": "SetNode", "pos": [2390, 1340], "size": [210, 58], "flags": {}, "order": 29, "mode": 0, "inputs": [{"label": "MODEL", "name": "MODEL", "type": "MODEL", "link": 65}], "outputs": [{"label": "*", "name": "*", "type": "*"}], "title": "Set_MODEL", "properties": {"previousName": "model2"}, "widgets_values": ["model2"], "color": "#223", "bgcolor": "#335"}, {"id": 55, "type": "LoraLoaderModelOnly", "pos": [1435.1138916015625, 1052.4095458984375], "size": [518.3076171875, 106.50037384033203], "flags": {}, "order": 20, "mode": 0, "inputs": [{"label": "model", "name": "model", "type": "MODEL", "link": 94}], "outputs": [{"label": "MODEL", "name": "MODEL", "type": "MODEL", "links": [59]}], "properties": {"Node name for S&R": "LoraLoaderModelOnly", "widget_ue_connectable": {}}, "widgets_values": ["Wan21_I2V_14B_lightx2v_cfg_step_distill_lora_rank64.safetensors", 1], "color": "#223", "bgcolor": "#335"}, {"id": 64, "type": "UNETLoader", "pos": [1444.385986328125, 1198.8485107421875], "size": [463.5276184082031, 88.93936920166016], "flags": {}, "order": 10, "mode": 0, "inputs": [], "outputs": [{"label": "MODEL", "name": "MODEL", "type": "MODEL", "slot_index": 0, "links": [67]}], "properties": {"Node name for S&R": "UNETLoader", "widget_ue_connectable": {}}, "widgets_values": ["wan2.2_i2v_low_noise_14B_fp8_scaled.safetensors", "fp8_e4m3fn"], "color": "#223", "bgcolor": "#335"}, {"id": 61, "type": "LoraLoaderModelOnly", "pos": [1441.8194580078125, 1329.993408203125], "size": [512.024658203125, 103.98723602294922], "flags": {}, "order": 19, "mode": 0, "inputs": [{"label": "model", "name": "model", "type": "MODEL", "link": 67}], "outputs": [{"label": "MODEL", "name": "MODEL", "type": "MODEL", "links": [63]}], "properties": {"Node name for S&R": "LoraLoaderModelOnly", "widget_ue_connectable": {}}, "widgets_values": ["Wan21_I2V_14B_lightx2v_cfg_step_distill_lora_rank64.safetensors", 1], "color": "#223", "bgcolor": "#335"}, {"id": 67, "type": "SetNode", "pos": [2400, 1520], "size": [210, 58], "flags": {}, "order": 16, "mode": 0, "inputs": [{"label": "CLIP", "name": "CLIP", "type": "CLIP", "link": 68}], "outputs": [{"label": "*", "name": "*", "type": "*"}], "title": "Set_CLIP", "properties": {"previousName": ""}, "widgets_values": ["CLIP"], "color": "#432", "bgcolor": "#653"}, {"id": 21, "type": "LayerUtility: ImageScaleByAspectRatio V2", "pos": [1414.8096923828125, 2094.648681640625], "size": [336, 330], "flags": {}, "order": 15, "mode": 0, "inputs": [{"label": "image", "name": "image", "shape": 7, "type": "IMAGE", "link": 18}, {"label": "mask", "name": "mask", "shape": 7, "type": "MASK"}, {"label": "scale_to_length", "name": "scale_to_length", "type": "INT", "widget": {"name": "scale_to_length"}, "link": 19}], "outputs": [{"label": "image", "name": "image", "type": "IMAGE", "links": [9, 32, 91]}, {"label": "mask", "name": "mask", "type": "MASK"}, {"label": "original_size", "name": "original_size", "type": "BOX"}, {"label": "width", "name": "width", "type": "INT", "links": [80]}, {"label": "height", "name": "height", "type": "INT", "links": [81]}], "properties": {"Node name for S&R": "LayerUtility: ImageScaleByAspectRatio V2"}, "widgets_values": ["original", 1, 1, "letterbox", "lanc<PERSON>s", "16", "longest", 1024, "#000000"], "color": "#223", "bgcolor": "#335"}, {"id": 80, "type": "LayerUtility: PurgeVRAM V2", "pos": [3059.27978515625, 2413.36767578125], "size": [305.9894714355469, 82], "flags": {}, "order": 34, "mode": 0, "inputs": [{"label": "anything", "name": "anything", "type": "*", "link": 92}], "outputs": [{"label": "any", "name": "any", "type": "*"}], "properties": {"Node name for S&R": "LayerUtility: PurgeVRAM V2", "widget_ue_connectable": {}}, "widgets_values": [true, true], "color": "rgba(38, 73, 116, 0.7)"}, {"id": 81, "type": "LayerUtility: PurgeVRAM V2", "pos": [3402.584716796875, 2410.76513671875], "size": [305.9894714355469, 82], "flags": {}, "order": 36, "mode": 0, "inputs": [{"label": "anything", "name": "anything", "type": "*", "link": 93}], "outputs": [{"label": "any", "name": "any", "type": "*"}], "properties": {"Node name for S&R": "LayerUtility: PurgeVRAM V2", "widget_ue_connectable": {}}, "widgets_values": [true, true], "color": "rgba(38, 73, 116, 0.7)"}, {"id": 73, "type": "KSamplerAdvanced", "pos": [3403.3330078125, 2026.66650390625], "size": [304.748046875, 334], "flags": {}, "order": 33, "mode": 0, "inputs": [{"label": "model", "name": "model", "type": "MODEL", "link": 72}, {"label": "positive", "name": "positive", "type": "CONDITIONING", "link": 87}, {"label": "negative", "name": "negative", "type": "CONDITIONING", "link": 88}, {"label": "latent_image", "name": "latent_image", "type": "LATENT", "link": 73}], "outputs": [{"label": "LATENT", "name": "LATENT", "type": "LATENT", "links": [70, 93]}], "properties": {"Node name for S&R": "KSamplerAdvanced", "widget_ue_connectable": {}}, "widgets_values": ["disable", 0, "fixed", 7, 1, "lcm", "simple", 3, 10000, "disable"], "color": "#323", "bgcolor": "#535"}, {"id": 30, "type": "RH_LLMAPI_NODE", "pos": [1495.5411376953125, 1881.404296875], "size": [357.7106628417969, 405.0201110839844], "flags": {"collapsed": true}, "order": 21, "mode": 0, "inputs": [{"label": "ref_image", "name": "ref_image", "shape": 7, "type": "IMAGE", "link": 32}, {"label": "prompt", "name": "prompt", "type": "STRING", "widget": {"name": "prompt"}, "link": 33}], "outputs": [{"label": "describe", "name": "describe", "type": "STRING", "links": [25]}], "properties": {"Node name for S&R": "RH_LLMAPI_NODE"}, "widgets_values": ["", "", "", "及时一个AI视频提示词专家，我需要你帮我根据参考图生成让图像动起来的提示词，要求：\n\n提示词=主体(主体描述)+场景(场景描述)+运动(运动描述)+镜头语言+氛围词+风格化主体描述:主体描述是对主体外观特征细节的描述，可通过形容词或短句列举，例如“一位身着少数民族服饰的黑发苗族少女”、\"一位来自异世界的飞天仙子，身着破旧却华丽的服饰，背后展开一对由废墟碎片构成的奇异翅膀”。场景描述:场景描述是对主体所处环境特征细节的描述，可通过形容词或短句列举运动描述:运动描述是对运动特征细节的描述，包含运动的幅度、速率和运动作用的效果，例如“猛烈地摇摆”、\"缓慢地移动\"、\"打碎了玻璃\"。镜头语言:镜头语言包含景别、视角、镜头、运镜等，常见镜头语言详见下方提示词词典。\n氛围词:氛围词是对预期画面氛围的描述，例如“梦幻\"、“孤独”、“宏伟\"，常见氛围词详见下方提示词词典。\n风格化:风格化是对画面风格语言的描述，例如“赛博朋克”、\"勾线插画”、\"废土风格\"，常见风格化详见下方提示词词典。\n\n参考案例如下：\n```\n一位身穿轻盈白色连衣裙的长发美女，肤色白皙，眼神温柔，微笑着，神情宁静。在金色的沙滩上，阳光明媚，海浪轻拍岸边，远处是碧蓝的大海与无边的天空交接，海风轻拂。她轻轻地在沙滩上步行，步伐优雅而缓慢，时而低头踩踏着海水，留下清晰的脚印，时而抬起头看向远方，微风吹动她的长发。镜头采用中景，稍微偏低的视角，以侧面跟随镜头运作，画面随她的步伐缓缓推进。镜头会偶尔拉近，捕捉她面部的柔和表情和细微的动作变化。宁静、柔和、浪漫、梦幻。清新自然的摄影风格，带有暖色调，画面略带柔焦效果，给人一种温暖的海边度假感。\n```\n\n请为我按照要求让图像动起来，提示词要求简洁通顺连贯，文字要求50字以内\n", "", 0.6, 1678, "randomize"], "color": "#223", "bgcolor": "#335"}, {"id": 72, "type": "KSamplerAdvanced", "pos": [3063.3330078125, 2036.6663818359375], "size": [304.748046875, 334], "flags": {}, "order": 32, "mode": 0, "inputs": [{"label": "model", "name": "model", "type": "MODEL", "link": 71}, {"label": "positive", "name": "positive", "type": "CONDITIONING", "link": 75}, {"label": "negative", "name": "negative", "type": "CONDITIONING", "link": 76}, {"label": "latent_image", "name": "latent_image", "type": "LATENT", "link": 89}], "outputs": [{"label": "LATENT", "name": "LATENT", "type": "LATENT", "links": [73, 92]}], "properties": {"Node name for S&R": "KSamplerAdvanced", "widget_ue_connectable": {}}, "widgets_values": ["enable", 550117708156447, "randomize", 7, 1, "euler", "simple", 0, 3, "enable"], "color": "#323", "bgcolor": "#535"}, {"id": 75, "type": "WanImageToVideo", "pos": [2684.846435546875, 2026.781494140625], "size": [342.5999755859375, 210], "flags": {}, "order": 31, "mode": 0, "inputs": [{"label": "positive", "name": "positive", "type": "CONDITIONING", "link": 77}, {"label": "negative", "name": "negative", "type": "CONDITIONING", "link": 78}, {"label": "vae", "name": "vae", "type": "VAE", "link": 79}, {"label": "clip_vision_output", "name": "clip_vision_output", "shape": 7, "type": "CLIP_VISION_OUTPUT"}, {"label": "start_image", "name": "start_image", "shape": 7, "type": "IMAGE", "link": 91}, {"label": "width", "name": "width", "type": "INT", "widget": {"name": "width"}, "link": 80}, {"label": "height", "name": "height", "type": "INT", "widget": {"name": "height"}, "link": 81}, {"label": "length", "name": "length", "type": "INT", "widget": {"name": "length"}, "link": 82}], "outputs": [{"label": "positive", "name": "positive", "type": "CONDITIONING", "slot_index": 0, "links": [75, 87]}, {"label": "negative", "name": "negative", "type": "CONDITIONING", "slot_index": 1, "links": [76, 88]}, {"label": "latent", "name": "latent", "type": "LATENT", "slot_index": 2, "links": [89]}], "properties": {"Node name for S&R": "WanImageToVideo", "widget_ue_connectable": {}}, "widgets_values": [704, 544, 81, 1], "color": "#432", "bgcolor": "#653"}, {"id": 83, "type": "UNETLoader", "pos": [1432.361328125, 910.5839233398438], "size": [490.9407958984375, 98.76153564453125], "flags": {}, "order": 11, "mode": 0, "inputs": [], "outputs": [{"label": "MODEL", "name": "MODEL", "type": "MODEL", "slot_index": 0, "links": [94]}], "properties": {"Node name for S&R": "UNETLoader", "widget_ue_connectable": {}}, "widgets_values": ["wan2.2_i2v_high_noise_14B_fp8_scaled.safetensors", "fp8_e4m3fn"], "color": "#223", "bgcolor": "#335"}, {"id": 82, "type": "Note", "pos": [1035.6790771484375, 1844.3043212890625], "size": [359.2687683105469, 129.67567443847656], "flags": {}, "order": 12, "mode": 0, "inputs": [], "outputs": [], "properties": {}, "widgets_values": ["本地的话，可以用GLM4插件替代：\n\n插件地址：https://github.com/msola-ht/ComfyUI-GLM4\n\n相关教程说明：https://mp.weixin.qq.com/s/b2pWEXWARzEEOBVhgZwFfQ"], "color": "#432", "bgcolor": "#653"}, {"id": 71, "type": "VAEDecode", "pos": [3733.567626953125, 2026.6663818359375], "size": [210, 46], "flags": {}, "order": 35, "mode": 0, "inputs": [{"label": "samples", "name": "samples", "type": "LATENT", "link": 70}, {"label": "vae", "name": "vae", "type": "VAE", "link": 86}], "outputs": [{"label": "IMAGE", "name": "IMAGE", "type": "IMAGE", "slot_index": 0, "links": [74]}], "properties": {"Node name for S&R": "VAEDecode", "widget_ue_connectable": {}}, "widgets_values": [], "color": "#323", "bgcolor": "#535"}, {"id": 27, "type": "ImpactSwitch", "pos": [3290.9716796875, 1385.837158203125], "size": [315, 122], "flags": {}, "order": 24, "mode": 0, "inputs": [{"label": "input1", "name": "input1", "shape": 7, "type": "STRING", "link": 24}, {"label": "input2", "name": "input2", "type": "STRING", "link": 25}, {"label": "input3", "name": "input3", "type": "STRING"}], "outputs": [{"label": "STRING", "name": "STRING", "type": "STRING", "links": [6, 35, 83]}, {"label": "selected_label", "name": "selected_label", "type": "STRING"}, {"label": "selected_index", "name": "selected_index", "type": "INT"}], "properties": {"Node name for S&R": "ImpactSwitch"}, "widgets_values": [2, false], "color": "#323", "bgcolor": "#535"}, {"id": 33, "type": "CR Text", "pos": [2816.42138671875, 1372.9892578125], "size": [400, 200], "flags": {}, "order": 13, "mode": 0, "inputs": [], "outputs": [{"label": "text", "name": "text", "type": "*", "links": [24, 33]}, {"label": "show_help", "name": "show_help", "type": "STRING", "links": []}], "properties": {"Node name for S&R": "CR Text"}, "widgets_values": ["第一人称视角，骑着自行车向前"], "color": "#223", "bgcolor": "#335"}, {"id": 32, "type": "easy showAnything", "pos": [1794.134765625, 1871.871337890625], "size": [312.4254150390625, 172.13632202148438], "flags": {}, "order": 27, "mode": 0, "inputs": [{"label": "anything", "name": "anything", "shape": 7, "type": "*", "link": 35}], "outputs": [{"label": "output", "name": "output", "type": "*"}], "properties": {"Node name for S&R": "easy showAnything"}, "widgets_values": ["主体：第一人称视角，双手握住自行车车把，车把上装饰着鲜花环。  \n场景：秋日乡间小路，两旁白桦树成排，阳光透过树叶洒下光斑，路上落满金黄树叶。  \n运动：自行车缓缓向前行驶，车轮碾过落叶，发出轻微沙沙声。  \n镜头语言：主观镜头，画面随车把轻微晃动，体现骑行的动态感。  \n氛围词：宁静、清新、温暖。  \n风格化：自然摄影风格，暖色调，带有轻微柔焦效果，营造舒适悠闲的氛围。"], "color": "#223", "bgcolor": "#335"}, {"id": 74, "type": "VHS_VideoCombine", "pos": [3632.991455078125, 912.1654663085938], "size": [305.81085205078125, 358], "flags": {}, "order": 37, "mode": 0, "inputs": [{"label": "images", "name": "images", "type": "IMAGE", "link": 74}, {"label": "audio", "name": "audio", "shape": 7, "type": "AUDIO"}, {"label": "meta_batch", "name": "meta_batch", "shape": 7, "type": "VHS_BatchManager"}, {"label": "vae", "name": "vae", "shape": 7, "type": "VAE"}], "outputs": [{"label": "Filenames", "name": "Filenames", "type": "VHS_FILENAMES"}], "properties": {"Node name for S&R": "VHS_VideoCombine", "cnr_id": "comfyui-videohelpersuite", "ver": "0a75c7958fe320efcb052f1d9f8451fd20c730a8", "widget_ue_connectable": {}}, "widgets_values": {"frame_rate": 24, "loop_count": 0, "filename_prefix": "WanVideoWrapper_VACE_startendframe", "format": "video/h264-mp4", "pix_fmt": "yuv420p", "crf": 19, "save_metadata": true, "trim_to_audio": false, "pingpong": false, "save_output": true, "no_preview": false, "videopreview": {"paused": false, "hidden": false, "params": {"filename": "WanVideoWrapper_VACE_startendframe_00001_tkkpn_1753761354.mp4", "workflow": "WanVideoWrapper_VACE_startendframe_00001.png", "fullpath": "/data/ComfyUI/personal/56ef2e7e7e8ebf423a67bb4f1713e678/output/WanVideoWrapper_VACE_startendframe_00001.mp4", "format": "video/h264-mp4", "subfolder": "", "type": "output", "frame_rate": 24}}}, "color": "#323", "bgcolor": "#535"}], "links": [[18, 29, 0, 21, 0, "IMAGE"], [19, 25, 0, 21, 2, "INT"], [23, 18, 0, 24, 0, "INT,FLOAT,IMAGE,LATENT"], [24, 33, 0, 27, 0, "STRING"], [25, 30, 0, 27, 1, "STRING"], [32, 21, 0, 30, 0, "IMAGE"], [33, 33, 0, 30, 1, "STRING"], [35, 27, 0, 32, 0, "*"], [59, 55, 0, 56, 0, "MODEL"], [60, 57, 0, 58, 0, "*"], [62, 56, 0, 57, 0, "MODEL"], [63, 61, 0, 59, 0, "MODEL"], [65, 63, 0, 62, 0, "*"], [66, 59, 0, 63, 0, "MODEL"], [67, 64, 0, 61, 0, "MODEL"], [68, 66, 0, 67, 0, "*"], [69, 65, 0, 68, 0, "*"], [70, 73, 0, 71, 0, "LATENT"], [71, 70, 0, 72, 0, "MODEL"], [72, 69, 0, 73, 0, "MODEL"], [73, 72, 0, 73, 3, "LATENT"], [74, 71, 0, 74, 0, "IMAGE"], [75, 75, 0, 72, 1, "CONDITIONING"], [76, 75, 1, 72, 2, "CONDITIONING"], [77, 76, 0, 75, 0, "CONDITIONING"], [78, 77, 0, 75, 1, "CONDITIONING"], [79, 78, 0, 75, 2, "VAE"], [80, 21, 3, 75, 5, "INT"], [81, 21, 4, 75, 6, "INT"], [82, 24, 0, 75, 7, "INT"], [83, 27, 0, 76, 1, "STRING"], [85, 79, 0, 76, 0, "CLIP"], [86, 78, 0, 71, 1, "VAE"], [87, 75, 0, 73, 1, "CONDITIONING"], [88, 75, 1, 73, 2, "CONDITIONING"], [89, 75, 2, 72, 3, "LATENT"], [90, 79, 0, 77, 0, "CLIP"], [91, 21, 0, 75, 4, "IMAGE"], [92, 72, 0, 80, 0, "*"], [93, 73, 0, 81, 0, "*"], [94, 83, 0, 55, 0, "MODEL"]], "groups": [{"id": 1, "title": "基础模型", "bounding": [1391.438232421875, 827.5772094726562, 1307.14111328125, 865.6385498046875], "color": "#88A", "font_size": 24, "flags": {}}, {"id": 2, "title": "图生视频", "bounding": [1404.8096923828125, 1729.693603515625, 2666.140380859375, 798.1962280273438], "color": "#3f789e", "font_size": 55, "flags": {}}, {"id": 3, "title": "B站、YouTube、公众号：嘟嘟AI绘画趣味学", "bounding": [1292.666748046875, 543, 2958.5419921875, 212.6490478515625], "color": "#b06634", "font_size": 150, "flags": {}}, {"id": 4, "title": "输入输出", "bounding": [2730.87255859375, 833.8777465820312, 1337.0928955078125, 860.0691528320312], "color": "#3f789e", "font_size": 24, "flags": {}}, {"id": 5, "title": "最大变长限制，推荐834", "bounding": [2862.66552734375, 1076.9539794921875, 355, 162], "color": "#3f789e", "font_size": 24, "flags": {}}, {"id": 6, "title": "视频要多少秒", "bounding": [2855.28662109375, 895.2007446289062, 355, 162], "color": "#3f789e", "font_size": 24, "flags": {}}, {"id": 7, "title": "提示词", "bounding": [2802.51708984375, 1293.6353759765625, 440, 304], "color": "#3f789e", "font_size": 24, "flags": {}}], "config": {}, "extra": {"ds": {"scale": 0.9646149645000033, "offset": [-2013.9412106186232, -1693.2685322855946]}, "VHS_KeepIntermediate": true, "links_added_by_ue": [], "ue_links": [], "VHS_MetadataImage": true, "0246.VERSION": [0, 0, 4], "VHS_latentpreviewrate": 0, "VHS_latentpreview": false}, "version": 0.4}