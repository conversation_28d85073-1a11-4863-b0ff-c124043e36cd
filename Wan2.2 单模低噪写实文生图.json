{"id": "25dfce61-3571-4c9a-a7f8-6a83951d66aa", "revision": 0, "last_node_id": 60, "last_link_id": 77, "nodes": [{"id": 4, "type": "CLIPTextEncode", "pos": [3650, 100], "size": [450, 150], "flags": {"collapsed": false}, "order": 21, "mode": 0, "inputs": [{"label": "clip", "name": "clip", "type": "CLIP", "link": 21}], "outputs": [{"label": "CONDITIONING", "name": "CONDITIONING", "type": "CONDITIONING", "links": [52]}], "properties": {"Node name for S&R": "CLIPTextEncode", "cnr_id": "comfy-core", "ver": "0.3.43"}, "widgets_values": [""], "color": "#2a363b", "bgcolor": "#3f5159"}, {"id": 9, "type": "VAEDecode", "pos": [4450, 100], "size": [250, 150], "flags": {}, "order": 24, "mode": 0, "inputs": [{"label": "samples", "name": "samples", "type": "LATENT", "link": 54}, {"label": "vae", "name": "vae", "type": "VAE", "link": 59}], "outputs": [{"label": "IMAGE", "name": "IMAGE", "type": "IMAGE", "links": [8]}], "properties": {"Node name for S&R": "VAEDecode", "cnr_id": "comfy-core", "ver": "0.3.43"}, "widgets_values": [], "color": "#323", "bgcolor": "#535"}, {"id": 29, "type": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "pos": [2890, -100], "size": [673.5929565429688, 156.85409545898438], "flags": {}, "order": 16, "mode": 0, "inputs": [{"label": "model", "name": "model", "type": "MODEL", "link": 39}, {"label": "clip", "name": "clip", "type": "CLIP", "link": 40}], "outputs": [{"label": "MODEL", "name": "MODEL", "type": "MODEL", "links": [35]}, {"label": "CLIP", "name": "CLIP", "type": "CLIP", "links": [36]}], "properties": {"Node name for S&R": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "cnr_id": "comfy-core", "ver": "0.3.43"}, "widgets_values": ["Wan21_T2V_14B_lightx2v_cfg_step_distill_lora_rank32.safetensors", 0.4000000000000001, 0.4000000000000001], "color": "#232", "bgcolor": "#353"}, {"id": 14, "type": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "pos": [2900, 110], "size": [655.886474609375, 135.7206573486328], "flags": {}, "order": 18, "mode": 0, "inputs": [{"label": "model", "name": "model", "type": "MODEL", "link": 35}, {"label": "clip", "name": "clip", "type": "CLIP", "link": 36}], "outputs": [{"label": "MODEL", "name": "MODEL", "type": "MODEL", "links": [61]}, {"label": "CLIP", "name": "CLIP", "type": "CLIP", "links": [21, 22]}], "properties": {"Node name for S&R": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "cnr_id": "comfy-core", "ver": "0.3.43"}, "widgets_values": ["WAN2.1_SmartphoneSnapshotPhotoReality_v1_by-AI_Characters.safetensors", 1.0000000000000002, 1.0000000000000002], "color": "#232", "bgcolor": "#353"}, {"id": 24, "type": "<PERSON>downNote", "pos": [3650, 500], "size": [450, 200], "flags": {}, "order": 0, "mode": 0, "inputs": [], "outputs": [], "properties": {}, "widgets_values": ["#我推荐全高清分辨率（1536x1536/1920x1088），以获得最佳质量，但如果你想节省VRAM和生成时间，你可以使用标准高清分辨率（960x960/1280x720），尽管这可能会减少很大的质量损失。"], "color": "#432", "bgcolor": "#653"}, {"id": 27, "type": "<PERSON>downNote", "pos": [4150, 300], "size": [550, 150], "flags": {}, "order": 1, "mode": 0, "inputs": [], "outputs": [], "properties": {}, "widgets_values": ["我测试了各种设置，发现这是最优的配置，以获得最佳的质量。如果你真的想节省生成时间，你可以将这些步骤减少到4步，但这可能会对质量产生明显的影响。"], "color": "#432", "bgcolor": "#653"}, {"id": 40, "type": "CLIPLoader", "pos": [2490, -110], "size": [360.6138610839844, 98], "flags": {}, "order": 2, "mode": 0, "inputs": [], "outputs": [{"label": "CLIP", "name": "CLIP", "type": "CLIP", "slot_index": 0, "links": [58]}], "properties": {"Node name for S&R": "CLIPLoader", "cnr_id": "comfy-core", "ver": "0.3.44", "models": [{"name": "umt5_xxl_fp8_e4m3fn_scaled.safetensors", "url": "https://huggingface.co/Comfy-Org/Wan_2.1_ComfyUI_repackaged/resolve/main/split_files/text_encoders/umt5_xxl_fp8_e4m3fn_scaled.safetensors?download=true", "directory": "text_encoders"}], "widget_ue_connectable": {}}, "widgets_values": ["umt5_xxl_fp8_e4m3fn_scaled.safetensors", "wan", "default"], "color": "#432", "bgcolor": "#653"}, {"id": 39, "type": "VAELoader", "pos": [2500, 60], "size": [367.7924499511719, 70.40070343017578], "flags": {}, "order": 3, "mode": 0, "inputs": [], "outputs": [{"label": "VAE", "name": "VAE", "type": "VAE", "slot_index": 0, "links": [59]}], "properties": {"Node name for S&R": "VAELoader", "cnr_id": "comfy-core", "ver": "0.3.44", "models": [{"name": "wan_2.1_vae.safetensors", "url": "https://huggingface.co/Comfy-Org/Wan_2.1_ComfyUI_repackaged/resolve/main/split_files/vae/wan_2.1_vae.safetensors?download=true", "directory": "vae"}], "widget_ue_connectable": {}}, "widgets_values": ["wan_2.1_vae.safetensors"], "color": "#322", "bgcolor": "#533"}, {"id": 30, "type": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "pos": [2900, -300], "size": [667.3099975585938, 145.4305877685547], "flags": {}, "order": 14, "mode": 0, "inputs": [{"label": "model", "name": "model", "type": "MODEL", "link": 77}, {"label": "clip", "name": "clip", "type": "CLIP", "link": 58}], "outputs": [{"label": "MODEL", "name": "MODEL", "type": "MODEL", "links": [39]}, {"label": "CLIP", "name": "CLIP", "type": "CLIP", "links": [40]}], "properties": {"Node name for S&R": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "cnr_id": "comfy-core", "ver": "0.3.43"}, "widgets_values": ["Wan2.1_T2V_14B_FusionX_LoRA.safetensors", 0.4000000000000001, 0.4000000000000001], "color": "#232", "bgcolor": "#353"}, {"id": 17, "type": "<PERSON>downNote", "pos": [4150, 500], "size": [550, 200], "flags": {}, "order": 4, "mode": 0, "inputs": [], "outputs": [], "properties": {}, "widgets_values": ["“res_2s” 和 “bong_tangent” 采样器与调度器分别来自 “Res4Lyf” 自定义节点。我发现到目前为止，这种组合所提供的质量最佳，但如果你愿意，也可以跳过该节点，仅使用 ComfyUI 的默认采样器和调度器之一。请注意，“res_2s” 大约会使生成时间翻倍。\n\nRes4Lyf插件地址：\nhttps://github.com/ClownsharkBatwing/RES4LYF\n\n\nRH节点还没上的话，先用euler+beta先用着，已经让他们上这个采样器插件了"], "color": "#432", "bgcolor": "#653"}, {"id": 34, "type": "K<PERSON><PERSON><PERSON>", "pos": [4150, -50], "size": [250, 300], "flags": {}, "order": 23, "mode": 0, "inputs": [{"label": "model", "name": "model", "type": "MODEL", "link": 62}, {"label": "positive", "name": "positive", "type": "CONDITIONING", "link": 51}, {"label": "negative", "name": "negative", "type": "CONDITIONING", "link": 52}, {"label": "latent_image", "name": "latent_image", "type": "LATENT", "link": 53}], "outputs": [{"label": "LATENT", "name": "LATENT", "type": "LATENT", "links": [54]}], "properties": {"Node name for S&R": "K<PERSON><PERSON><PERSON>", "cnr_id": "comfy-core", "ver": "0.3.43"}, "widgets_values": [686580065475578, "randomize", 8, 1, "res_2s", "bong_tangent", 1], "color": "#223", "bgcolor": "#335"}, {"id": 3, "type": "CLIPTextEncode", "pos": [3650, -300], "size": [450, 350], "flags": {}, "order": 22, "mode": 0, "inputs": [{"label": "clip", "name": "clip", "type": "CLIP", "link": 22}, {"label": "text", "name": "text", "type": "STRING", "widget": {"name": "text"}, "link": 64}], "outputs": [{"label": "CONDITIONING", "name": "CONDITIONING", "type": "CONDITIONING", "links": [51]}], "properties": {"Node name for S&R": "CLIPTextEncode", "cnr_id": "comfy-core", "ver": "0.3.43"}, "widgets_values": [" 描绘了一位年轻女性脸部特写, 被透过薄纱窗帘的金色晨光照亮, 从侧面拍摄.\n在紧凑的1:1特写镜头中, 仅可见女性脸庞的一半, 略微转向附近的一扇窗户, 温暖, 清晨的阳光透过白色布帘倾泻而入.光线柔和地掠过她的面容, 突显出她脸颊上细腻的桃色绒毛和嘴唇附近干燥皮肤的微弱纹理.她的眼睛在画框边缘刚刚可见, 捕捉到一丝琥珀色光芒.漂浮在空气中的杂乱发丝在逆光中发光.背景模糊不清但显然是室内--可能是卧室--失焦并染上日黄色调.她表情中的轻微眯眼可见清晨的昏沉感.镜头在她脸部最亮的部分周围形成淡淡的光晕, 光线最强的区域有轻微的色彩溢出.感觉非常亲密, 像是醒来后拍下的第一张照片, 在寂静中捕捉."], "color": "#2a363b", "bgcolor": "#3f5159"}, {"id": 5, "type": "EmptyHunyuanLatentVideo", "pos": [3651.672607421875, 304.1812438964844], "size": [450, 150], "flags": {}, "order": 12, "mode": 0, "inputs": [{"label": "width", "name": "width", "type": "INT", "widget": {"name": "width"}, "link": 75}, {"label": "height", "name": "height", "type": "INT", "widget": {"name": "height"}, "link": 76}], "outputs": [{"label": "LATENT", "name": "LATENT", "type": "LATENT", "links": [53]}], "properties": {"Node name for S&R": "EmptyHunyuanLatentVideo", "cnr_id": "comfy-core", "ver": "0.3.43"}, "widgets_values": [1536, 1536, 1, 1], "color": "#323", "bgcolor": "#535"}, {"id": 59, "type": "ImpactInt", "pos": [2514.88427734375, 1113.7144775390625], "size": [315, 58], "flags": {}, "order": 5, "mode": 0, "inputs": [], "outputs": [{"label": "INT", "name": "INT", "type": "INT", "links": [76]}], "properties": {"Node name for S&R": "ImpactInt", "widget_ue_connectable": {}}, "widgets_values": [1536], "color": "#223", "bgcolor": "#335"}, {"id": 43, "type": "CFGZeroStarAndInit", "pos": [4149.232421875, -262.97650146484375], "size": [315, 82], "flags": {}, "order": 20, "mode": 0, "inputs": [{"label": "model", "name": "model", "type": "MODEL", "link": 61}], "outputs": [{"label": "MODEL", "name": "MODEL", "type": "MODEL", "links": [62]}], "properties": {"Node name for S&R": "CFGZeroStarAndInit", "widget_ue_connectable": {}}, "widgets_values": [true, 0], "color": "#323", "bgcolor": "#535"}, {"id": 53, "type": "ImpactSwitch", "pos": [3284.884521484375, 963.7144775390625], "size": [315, 122], "flags": {}, "order": 15, "mode": 0, "inputs": [{"label": "input1", "name": "input1", "shape": 7, "type": "STRING", "link": 73}, {"label": "input2", "name": "input2", "type": "STRING", "link": 74}, {"label": "input3", "name": "input3", "type": "STRING"}], "outputs": [{"label": "STRING", "name": "STRING", "type": "STRING", "links": [71]}, {"label": "selected_label", "name": "selected_label", "type": "STRING"}, {"label": "selected_index", "name": "selected_index", "type": "INT"}], "properties": {"Node name for S&R": "ImpactSwitch", "widget_ue_connectable": {}}, "widgets_values": [2, false], "color": "#223", "bgcolor": "#335"}, {"id": 58, "type": "ImpactInt", "pos": [2514.88427734375, 983.7142944335938], "size": [315, 58], "flags": {}, "order": 6, "mode": 0, "inputs": [], "outputs": [{"label": "INT", "name": "INT", "type": "INT", "links": [75]}], "properties": {"Node name for S&R": "ImpactInt", "widget_ue_connectable": {}}, "widgets_values": [1536], "color": "#223", "bgcolor": "#335"}, {"id": 47, "type": "CR Text Concatenate", "pos": [2850, 310], "size": [315, 126], "flags": {}, "order": 17, "mode": 0, "inputs": [{"label": "text1", "name": "text1", "shape": 7, "type": "STRING", "widget": {"name": "text1"}, "link": 63}, {"label": "text2", "name": "text2", "shape": 7, "type": "STRING", "widget": {"name": "text2"}, "link": 71}], "outputs": [{"label": "STRING", "name": "STRING", "type": "*", "links": [64, 66]}, {"label": "show_help", "name": "show_help", "type": "STRING"}], "properties": {"Node name for S&R": "CR Text Concatenate", "widget_ue_connectable": {}}, "widgets_values": ["", "", ""], "color": "#232", "bgcolor": "#353"}, {"id": 49, "type": "easy showAnything", "pos": [2765.959228515625, 494.21636962890625], "size": [398.4926452636719, 296.8557434082031], "flags": {}, "order": 19, "mode": 0, "inputs": [{"label": "anything", "name": "anything", "shape": 7, "type": "*", "link": 66}], "outputs": [{"label": "output", "name": "output", "type": "*"}], "properties": {"Node name for S&R": "easy showAnything", "widget_ue_connectable": {}}, "widgets_values": ["2010年代初用手机拍摄并上传到Facebook的快照照片, 具有动态自然光照, 以及中性白色色彩平衡和褪色效果,一位年轻的亚洲女性正专注地坐在书桌前办公，表情沉静而专注，透着一丝智慧与干练。她戴着一副黑色框的眼镜，镜片反射出屏幕的微弱光芒，增添了几分知性的气质。她身穿一件精致的白色蕾丝衬衣，褶皱与镂空花纹细腻优雅，展现出柔美与专业的完美结合。书桌上摆放着一台笔记本电脑、几本打开的书籍和一杯冒着热气的咖啡，周围的环境布置简洁而现代，灯光柔和，散发出温暖的氛围。背景隐约可见一面书架，上面整齐地排列着各类书籍，整体画面透露着一种平静而充实的工作情境。"], "color": "#232", "bgcolor": "#353"}, {"id": 54, "type": "Note", "pos": [3311.684326171875, 1126.40087890625], "size": [270.4560546875, 88], "flags": {}, "order": 7, "mode": 0, "inputs": [], "outputs": [], "properties": {}, "widgets_values": ["1:自定义写提示词\n2：智能扩写提示词\n"], "color": "#432", "bgcolor": "#653"}, {"id": 10, "type": "SaveImage", "pos": [3766.974365234375, 891.6638793945312], "size": [883.9317016601562, 930.977783203125], "flags": {}, "order": 25, "mode": 0, "inputs": [{"label": "images", "name": "images", "type": "IMAGE", "link": 8}], "outputs": [], "properties": {"Node name for S&R": "SaveImage", "cnr_id": "comfy-core", "ver": "0.3.43"}, "widgets_values": ["ComfyUI", ""], "color": "#233", "bgcolor": "#355"}, {"id": 46, "type": "CR Text", "pos": [2854.88427734375, 963.7144775390625], "size": [400, 200], "flags": {}, "order": 8, "mode": 0, "inputs": [], "outputs": [{"label": "text", "name": "text", "type": "*", "links": [72, 73]}, {"label": "show_help", "name": "show_help", "type": "STRING"}], "properties": {"Node name for S&R": "CR Text", "widget_ue_connectable": {}}, "widgets_values": ["一个亚洲女性在书桌上办公，黑色眼镜，蕾丝衬衣"], "color": "#223", "bgcolor": "#335"}, {"id": 60, "type": "UNETLoader", "pos": [2445.627197265625, -278.52984619140625], "size": [420.1534118652344, 83.15281677246094], "flags": {}, "order": 9, "mode": 0, "inputs": [], "outputs": [{"label": "MODEL", "name": "MODEL", "type": "MODEL", "slot_index": 0, "links": [77]}], "properties": {"Node name for S&R": "UNETLoader", "cnr_id": "comfy-core", "ver": "0.3.44", "models": [{"name": "wan2.1_t2v_1.3B_fp16.safetensors", "url": "https://huggingface.co/Comfy-Org/Wan_2.1_ComfyUI_repackaged/resolve/main/split_files/diffusion_models/wan2.1_t2v_1.3B_fp16.safetensors?download=true", "directory": "diffusion_models"}], "widget_ue_connectable": {}}, "widgets_values": ["wan2.2_t2v_low_noise_14B_fp8_scaled.safetensors", "fp8_e4m3fn"], "color": "#223", "bgcolor": "#335"}, {"id": 51, "type": "RH_LLMAPI_NODE", "pos": [2432.023681640625, 492.1424560546875], "size": [274.411865234375, 286], "flags": {}, "order": 13, "mode": 0, "inputs": [{"label": "ref_image", "name": "ref_image", "shape": 7, "type": "IMAGE"}, {"label": "prompt", "name": "prompt", "type": "STRING", "widget": {"name": "prompt"}, "link": 72}], "outputs": [{"label": "describe", "name": "describe", "type": "STRING", "links": [70, 74]}], "properties": {"Node name for S&R": "RH_LLMAPI_NODE", "widget_ue_connectable": {}}, "widgets_values": ["", "", "", "# FLUX Prompt 反推提示词说明\n\n## 🧠 角色定位\n你是一位富有艺术感的 **FLUX Prompt 助理**，具备对图像的深度理解能力，能够将视觉内容转化为丰富、生动、具体的文本提示词（prompt），以用于图像生成模型 FLUX 或 Stable Diffusion。\n\n---\n\n## 🎯 核心任务\n\n我将提供一张图片或一个简短的主题描述，你的任务是：\n\n1. **理解图像/主题**：分析图像中的内容、元素、情感与风格。\n2. **生成 Prompt**：根据分析结果，输出一段详尽的英文 prompt，用于高质量图像生成。\n\n---\n\n## 🖼️ 图像分析维度\n\n请从以下角度描述图像内容，确保 prompt 丰富、准确、具象：\n\n- **主要元素**：人物、动物、物体、风景等核心对象\n- **画面细节**：颜色、纹理、光影、服饰、姿态、动作、表情、环境构成等（不少于5处具体细节）\n- **场景氛围**：温馨、神秘、奇幻、宁静、末世感等\n- **艺术风格**：现实主义、赛博朋克、油画风、水彩、卡通、像素风、未来主义等\n- **构图视角**：如“俯视”、“仰视”、“特写”、“广角”等\n\n---\n\n## ✏️ Prompt 输出格式要求\n\n- **语言**：仅使用中文生成 prompt\n- **语气**：描述性强、画面感明确，避免口语化或模糊措辞\n- **结构**：连贯自然，不分条目，形成一段完整描述\n- **长度**：足够详尽，建议不少于60词\n- **内容限制**：\n  - 不解释 prompt 内容\n  - 不添加“生成提示词”、“Prompt:”等前缀\n\n---\n\n## ✅ 示例\n\n- **输入主题**：一只飞在雪山上的龙\n- **输出 prompt**：\n\n  > 一条雄伟的绿鳞巨龙，眼中泛着琥珀色光芒，双翼张开，飞翔在令人叹为观止的雪山群中。它强壮的身影投下长长的阴影，笼罩着高耸入云的山峰。下方是一条清澈的河流，在深谷中蜿蜒流淌，倒映着明亮的天空。空气中弥漫着飘渺的薄雾，营造出清新而梦幻的氛围。这幅画面展现了令人敬畏的自然与野性之美。", "", 0.6, 1543, "randomize"], "color": "#232", "bgcolor": "#353"}, {"id": 25, "type": "<PERSON>downNote", "pos": [3192.533447265625, 306.8741455078125], "size": [400, 400], "flags": {}, "order": 10, "mode": 0, "inputs": [], "outputs": [], "properties": {}, "widgets_values": ["我建议同时使用lightx2v和FusionX LoRa，因为它们分别将模型步骤和CFG压缩到4/8和1，从而大大加快了生成时间。我建议同时使用这两种方法，因为它们可以很好地互补，这样你就可以避免它们各自的缺点，这是一个非常人工和虚假的AI外观。我发现两者都是0.4效果最好。\n\n\n你也可以安全地在他们旁边加载任何其他普通的LoRa。"], "color": "#432", "bgcolor": "#653"}, {"id": 45, "type": "CR Text", "pos": [2420, 320], "size": [401.5205078125, 119.41521453857422], "flags": {}, "order": 11, "mode": 0, "inputs": [], "outputs": [{"label": "text", "name": "text", "type": "*", "links": [63]}, {"label": "show_help", "name": "show_help", "type": "STRING"}], "properties": {"Node name for S&R": "CR Text", "widget_ue_connectable": {}}, "widgets_values": ["2010年代初用手机拍摄并上传到Facebook的快照照片, 具有动态自然光照, 以及中性白色色彩平衡和褪色效果,"], "color": "#232", "bgcolor": "#353"}], "links": [[8, 9, 0, 10, 0, "IMAGE"], [21, 14, 1, 4, 0, "CLIP"], [22, 14, 1, 3, 0, "CLIP"], [35, 29, 0, 14, 0, "MODEL"], [36, 29, 1, 14, 1, "CLIP"], [39, 30, 0, 29, 0, "MODEL"], [40, 30, 1, 29, 1, "CLIP"], [51, 3, 0, 34, 1, "CONDITIONING"], [52, 4, 0, 34, 2, "CONDITIONING"], [53, 5, 0, 34, 3, "LATENT"], [54, 34, 0, 9, 0, "LATENT"], [58, 40, 0, 30, 1, "CLIP"], [59, 39, 0, 9, 1, "VAE"], [61, 14, 0, 43, 0, "MODEL"], [62, 43, 0, 34, 0, "MODEL"], [63, 45, 0, 47, 0, "STRING"], [64, 47, 0, 3, 1, "STRING"], [66, 47, 0, 49, 0, "*"], [70, 51, 0, 53, 0, "STRING"], [71, 53, 0, 47, 1, "STRING"], [72, 46, 0, 51, 1, "STRING"], [73, 46, 0, 53, 0, "STRING"], [74, 51, 0, 53, 1, "STRING"], [75, 58, 0, 5, 0, "INT"], [76, 59, 0, 5, 1, "INT"], [77, 60, 0, 30, 0, "MODEL"]], "groups": [{"id": 1, "title": "B站、YouTube、公众号：嘟嘟AI绘画趣味学", "bounding": [2400.51513671875, -621.8938598632812, 2362.104248046875, 159.00135803222656], "color": "#b06634", "font_size": 120, "flags": {}}, {"id": 2, "title": "入参", "bounding": [2494.88427734375, 879.7144775390625, 1144.2337646484375, 394.7895202636719], "color": "#3f789e", "font_size": 24, "flags": {}}, {"id": 3, "title": "Group", "bounding": [2400, -384, 2320, 1195.0721435546875], "color": "#3f789e", "font_size": 24, "flags": {}}], "config": {}, "extra": {"ds": {"scale": 0.49500000000000144, "offset": [-1232.7761827028012, 706.4278837595798]}, "VHS_KeepIntermediate": true, "links_added_by_ue": [], "VHS_MetadataImage": true, "ue_links": [], "0246.VERSION": [0, 0, 4], "VHS_latentpreviewrate": 0, "frontendVersion": "1.23.4", "VHS_latentpreview": false}, "version": 0.4}